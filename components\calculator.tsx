"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Di<PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Minus, X, RefreshCw, Save, RotateCcw, Trash2, TrendingUp, TrendingDown } from "lucide-react"
import { fruitsData, type Fruit, type ApiFruit, mergeFruitData } from "@/lib/fruits-data"

interface SelectedFruit extends Fruit {
  quantity: number
}

interface CalculatorProps {
  dict: any
}

export function Calculator({ dict }: CalculatorProps) {
  const [fruits, setFruits] = useState<Fruit[]>(fruitsData)
  const [yourFruits, setYourFruits] = useState<SelectedFruit[]>([])
  const [theirFruits, setTheirFruits] = useState<SelectedFruit[]>([])
  const [loading, setLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<string>("")
  const [showFruitSelector, setShowFruitSelector] = useState(false)
  const [selectorFor, setSelectorFor] = useState<"yours" | "theirs">("yours")

  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [saveName, setSaveName] = useState("")
  const [savedCalculations, setSavedCalculations] = useState<any[]>([])

  // Fetch and merge API data
  const fetchApiData = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/fruits")
      if (response.ok) {
        const data = await response.json()
        const apiData: ApiFruit[] = data.fruits || []

        const mergedFruits = fruitsData.map((staticFruit) => {
          const apiMatch = apiData.find((api) => api.name.toLowerCase() === staticFruit.name.toLowerCase())
          return mergeFruitData(staticFruit, apiMatch)
        })

        setFruits(mergedFruits)
        setLastUpdated(data.lastUpdated || new Date().toISOString())
      }
    } catch (error) {
      console.error("Error fetching API data:", error)
      setFruits(fruitsData)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchApiData()
    const interval = setInterval(fetchApiData, 1 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    loadSavedCalculations()
  }, [])

  const openFruitSelector = (forSide: "yours" | "theirs") => {
    setSelectorFor(forSide)
    setShowFruitSelector(true)
  }

  const addFruit = (fruit: Fruit) => {
    const selectedFruit: SelectedFruit = { ...fruit, quantity: 1 }

    if (selectorFor === "yours") {
      setYourFruits((prev) => {
        const existing = prev.find((f) => f.name === fruit.name)
        if (existing) {
          return prev.map((f) => (f.name === fruit.name ? { ...f, quantity: f.quantity + 1 } : f))
        }
        return [...prev, selectedFruit]
      })
    } else {
      setTheirFruits((prev) => {
        const existing = prev.find((f) => f.name === fruit.name)
        if (existing) {
          return prev.map((f) => (f.name === fruit.name ? { ...f, quantity: f.quantity + 1 } : f))
        }
        return [...prev, selectedFruit]
      })
    }
    setShowFruitSelector(false)
  }

  const removeFruit = (fruitName: string, isYours: boolean) => {
    if (isYours) {
      setYourFruits((prev) => prev.filter((f) => f.name !== fruitName))
    } else {
      setTheirFruits((prev) => prev.filter((f) => f.name !== fruitName))
    }
  }

  const updateQuantity = (fruitName: string, isYours: boolean, change: number) => {
    const updateList = isYours ? setYourFruits : setTheirFruits

    updateList((prev) =>
      prev.map((f) => {
        if (f.name === fruitName) {
          const newQuantity = Math.max(1, f.quantity + change)
          return { ...f, quantity: newQuantity }
        }
        return f
      }),
    )
  }

  const calculateTotal = (fruitList: SelectedFruit[]) => {
    return fruitList.reduce((total, fruit) => total + fruit.value * fruit.quantity, 0)
  }

  const formatValue = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`
    }
    return `$${value.toLocaleString()}`
  }

  const yourTotal = calculateTotal(yourFruits)
  const theirTotal = calculateTotal(theirFruits)
  const difference = theirTotal - yourTotal

  // Improved trade analysis calculation
  const getTradeAnalysis = (yourValue: number, theirValue: number) => {
    if (yourValue === 0 && theirValue === 0) {
      return {
        difference: 0,
        advantagePercent: 0,
        winner: "none" as const,
        rating: { rating: dict.calculator.ratings.fair, color: "text-gray-400" },
        advantageText: dict.calculator.noTrade,
      }
    }

    if (yourValue === 0) {
      return {
        difference: theirValue,
        advantagePercent: 100,
        winner: "you" as const,
        rating: { rating: dict.calculator.ratings.excellent, color: "text-green-400" },
        advantageText: dict.calculator.youWin,
      }
    }

    if (theirValue === 0) {
      return {
        difference: -yourValue,
        advantagePercent: 100,
        winner: "them" as const,
        rating: { rating: dict.calculator.ratings.terrible, color: "text-red-400" },
        advantageText: dict.calculator.youLose,
      }
    }

    const diff = theirValue - yourValue
    const totalValue = Math.max(yourValue, theirValue)
    const advantagePercent = Math.abs(diff / totalValue) * 100

    let winner: "you" | "them" | "fair"
    let advantageText: string
    let rating: { rating: string; color: string }

    if (Math.abs(diff) < totalValue * 0.05) {
      // Less than 5% difference
      winner = "fair"
      advantageText = dict.calculator.fairTrade
      rating = { rating: dict.calculator.ratings.fair, color: "text-green-400" }
    } else if (diff > 0) {
      // You receive more value
      winner = "you"
      advantageText = dict.calculator.youWin
      if (advantagePercent <= 15) {
        rating = { rating: dict.calculator.ratings.good, color: "text-green-400" }
      } else if (advantagePercent <= 30) {
        rating = { rating: dict.calculator.ratings.great, color: "text-green-500" }
      } else {
        rating = { rating: dict.calculator.ratings.excellent, color: "text-green-600" }
      }
    } else {
      // You give more value
      winner = "them"
      advantageText = dict.calculator.youLose
      if (advantagePercent <= 15) {
        rating = { rating: dict.calculator.ratings.okay, color: "text-yellow-400" }
      } else if (advantagePercent <= 30) {
        rating = { rating: dict.calculator.ratings.bad, color: "text-orange-400" }
      } else {
        rating = { rating: dict.calculator.ratings.terrible, color: "text-red-400" }
      }
    }

    return {
      difference: diff,
      advantagePercent,
      winner,
      rating,
      advantageText,
    }
  }

  const tradeAnalysis = getTradeAnalysis(yourTotal, theirTotal)

  const resetCalculator = () => {
    setYourFruits([])
    setTheirFruits([])
  }

  // Load saved calculations from localStorage
  const loadSavedCalculations = () => {
    try {
      const saved = localStorage.getItem("blox-fruits-calculations")
      if (saved) {
        setSavedCalculations(JSON.parse(saved))
      }
    } catch (error) {
      console.error("Error loading saved calculations:", error)
    }
  }

  // Save calculation to localStorage
  const saveCalculation = () => {
    if (!saveName.trim()) return

    const calculation = {
      id: Date.now(),
      name: saveName.trim(),
      yourFruits,
      theirFruits,
      yourTotal,
      theirTotal,
      difference,
      createdAt: new Date().toISOString(),
    }

    const updated = [...savedCalculations, calculation]
    setSavedCalculations(updated)
    localStorage.setItem("blox-fruits-calculations", JSON.stringify(updated))

    setSaveName("")
    setShowSaveDialog(false)
  }

  // Load a saved calculation
  const loadCalculation = (calculation: any) => {
    // Update fruits with current API values
    const updatedYourFruits = calculation.yourFruits.map((savedFruit: any) => {
      const currentFruit = fruits.find((f) => f.name === savedFruit.name)
      return currentFruit ? { ...currentFruit, quantity: savedFruit.quantity } : savedFruit
    })

    const updatedTheirFruits = calculation.theirFruits.map((savedFruit: any) => {
      const currentFruit = fruits.find((f) => f.name === savedFruit.name)
      return currentFruit ? { ...currentFruit, quantity: savedFruit.quantity } : savedFruit
    })

    setYourFruits(updatedYourFruits)
    setTheirFruits(updatedTheirFruits)
  }

  // Calculate updated values for saved calculations
  const getUpdatedCalculationValues = (calculation: any) => {
    const updatedYourFruits = calculation.yourFruits.map((savedFruit: any) => {
      const currentFruit = fruits.find((f) => f.name === savedFruit.name)
      return currentFruit ? { ...currentFruit, quantity: savedFruit.quantity } : savedFruit
    })

    const updatedTheirFruits = calculation.theirFruits.map((savedFruit: any) => {
      const currentFruit = fruits.find((f) => f.name === savedFruit.name)
      return currentFruit ? { ...currentFruit, quantity: savedFruit.quantity } : savedFruit
    })

    const newYourTotal = calculateTotal(updatedYourFruits)
    const newTheirTotal = calculateTotal(updatedTheirFruits)

    return {
      yourTotal: newYourTotal,
      theirTotal: newTheirTotal,
      yourFruits: updatedYourFruits,
      theirFruits: updatedTheirFruits,
      analysis: getTradeAnalysis(newYourTotal, newTheirTotal),
    }
  }

  // Delete a saved calculation
  const deleteCalculation = (id: number) => {
    const updatedCalculations = savedCalculations.filter((calc) => calc.id !== id)
    setSavedCalculations(updatedCalculations)
    localStorage.setItem("blox-fruits-calculations", JSON.stringify(updatedCalculations))
  }

  // Get all fruits sorted by value for selector
  const allFruits = fruits.sort((a, b) => b.value - a.value)

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-5xl font-bold text-white mb-4">{dict.calculator.title}</h1>
          <p className="text-gray-400 text-lg mb-6">{dict.calculator.subtitle}</p>

          {/* API Status */}
          <div className="flex items-center justify-center gap-4">
            <Badge variant="outline" className="border-green-500/50 text-green-400">
              {dict.calculator.liveValues}
            </Badge>
            {lastUpdated && (
              <span className="text-sm text-gray-500">
                {dict.calculator.updated}: {new Date(lastUpdated).toLocaleTimeString()}
              </span>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={fetchApiData}
              disabled={loading}
              className="border-white/20 text-white hover:bg-white/10"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`} />
              {dict.calculator.refresh}
            </Button>
          </div>
        </div>

        {/* Trading Interface */}
        <div className="grid lg:grid-cols-2 gap-8 mb-8">
          {/* Your Offer */}
          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-2xl font-bold text-green-400 text-center">
                {dict.calculator.yourOffer}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Selected Fruits */}
              <div className="space-y-3 min-h-[200px]">
                {yourFruits.map((fruit) => (
                  <div key={fruit.name} className="bg-gray-700/50 rounded-lg p-4 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 rounded-lg bg-gray-600 flex items-center justify-center overflow-hidden">
                        <img
                          src={fruit.image || "/placeholder.svg"}
                          alt={fruit.name}
                          className="w-10 h-10 object-contain"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = "/placeholder.svg?height=40&width=40"
                          }}
                        />
                      </div>
                      <div>
                        <div className="text-white font-semibold">{fruit.name}</div>
                        <div className="text-green-400 text-sm">{formatValue(fruit.value)}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(fruit.name, true, -1)}
                          className="w-8 h-8 p-0 border-gray-600 text-white hover:bg-gray-600"
                        >
                          <Minus className="w-4 h-4" />
                        </Button>
                        <span className="text-white w-8 text-center font-semibold">{fruit.quantity}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(fruit.name, true, 1)}
                          className="w-8 h-8 p-0 border-gray-600 text-white hover:bg-gray-600"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeFruit(fruit.name, true)}
                        className="text-red-400 hover:text-red-300 hover:bg-red-400/10"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Add Fruit Button */}
              <Button
                onClick={() => openFruitSelector("yours")}
                className="w-full h-16 border-2 border-dashed border-gray-600 bg-transparent hover:bg-gray-700/30 text-gray-400 hover:text-white transition-all"
                variant="outline"
              >
                <Plus className="w-6 h-6 mr-2" />
                {dict.calculator.addFruit}
              </Button>

              {/* Total Value */}
              <div className="border-t border-gray-600 pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400 text-lg">{dict.calculator.totalValue}:</span>
                  <span className="text-green-400 text-2xl font-bold">{formatValue(yourTotal)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Their Offer */}
          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-2xl font-bold text-purple-400 text-center">
                {dict.calculator.theirOffer}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Selected Fruits */}
              <div className="space-y-3 min-h-[200px]">
                {theirFruits.map((fruit) => (
                  <div key={fruit.name} className="bg-gray-700/50 rounded-lg p-4 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 rounded-lg bg-gray-600 flex items-center justify-center overflow-hidden">
                        <img
                          src={fruit.image || "/placeholder.svg"}
                          alt={fruit.name}
                          className="w-10 h-10 object-contain"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = "/placeholder.svg?height=40&width=40"
                          }}
                        />
                      </div>
                      <div>
                        <div className="text-white font-semibold">{fruit.name}</div>
                        <div className="text-purple-400 text-sm">{formatValue(fruit.value)}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(fruit.name, false, -1)}
                          className="w-8 h-8 p-0 border-gray-600 text-white hover:bg-gray-600"
                        >
                          <Minus className="w-4 h-4" />
                        </Button>
                        <span className="text-white w-8 text-center font-semibold">{fruit.quantity}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(fruit.name, false, 1)}
                          className="w-8 h-8 p-0 border-gray-600 text-white hover:bg-gray-600"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeFruit(fruit.name, false)}
                        className="text-red-400 hover:text-red-300 hover:bg-red-400/10"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Add Fruit Button */}
              <Button
                onClick={() => openFruitSelector("theirs")}
                className="w-full h-16 border-2 border-dashed border-gray-600 bg-transparent hover:bg-gray-700/30 text-gray-400 hover:text-white transition-all"
                variant="outline"
              >
                <Plus className="w-6 h-6 mr-2" />
                {dict.calculator.addFruit}
              </Button>

              {/* Total Value */}
              <div className="border-t border-gray-600 pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400 text-lg">{dict.calculator.totalValue}:</span>
                  <span className="text-purple-400 text-2xl font-bold">{formatValue(theirTotal)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Trade Analysis */}
        {(yourFruits.length > 0 || theirFruits.length > 0) && (
          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm mb-8">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-white text-center flex items-center justify-center gap-2">
                {dict.calculator.tradeAnalysis}
                {tradeAnalysis.winner === "you" && <TrendingUp className="w-6 h-6 text-green-400" />}
                {tradeAnalysis.winner === "them" && <TrendingDown className="w-6 h-6 text-red-400" />}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
                <div>
                  <div
                    className={`text-3xl font-bold mb-2 ${tradeAnalysis.difference < 0 ? "text-red-400" : tradeAnalysis.difference > 0 ? "text-green-400" : "text-gray-400"}`}
                  >
                    {tradeAnalysis.difference === 0 ? "±" : tradeAnalysis.difference < 0 ? "-" : "+"}
                    {formatValue(Math.abs(tradeAnalysis.difference))}
                  </div>
                  <div className="text-gray-400">{dict.calculator.valueDifference}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-yellow-400 mb-2">
                    {tradeAnalysis.advantagePercent.toFixed(1)}%
                  </div>
                  <div className="text-gray-400">{dict.calculator.advantage}</div>
                </div>
                <div>
                  <div className={`text-2xl font-bold mb-2 ${tradeAnalysis.rating.color}`}>
                    {tradeAnalysis.rating.rating}
                  </div>
                  <div className="text-gray-400">{dict.calculator.tradeRating}</div>
                </div>
                <div>
                  <div
                    className={`text-xl font-bold mb-2 ${tradeAnalysis.winner === "you" ? "text-green-400" : tradeAnalysis.winner === "them" ? "text-red-400" : "text-gray-400"}`}
                  >
                    {tradeAnalysis.advantageText}
                  </div>
                  <div className="text-gray-400">{dict.calculator.result}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex justify-center gap-4">
          <Button
            onClick={() => setShowSaveDialog(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 text-lg"
            disabled={yourFruits.length === 0 && theirFruits.length === 0}
          >
            <Save className="w-5 h-5 mr-2" />
            {dict.calculator.saveCalculation}
          </Button>
          <Button
            variant="outline"
            onClick={resetCalculator}
            className="border-gray-600 text-white hover:bg-gray-700 px-8 py-3 text-lg"
            disabled={yourFruits.length === 0 && theirFruits.length === 0}
          >
            <RotateCcw className="w-5 h-5 mr-2" />
            {dict.calculator.reset}
          </Button>
        </div>

        {/* Saved Calculations Section */}
        {savedCalculations.length > 0 && (
          <div className="mt-12">
            <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-white text-center">
                  {dict.calculator.savedCalculations}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  {savedCalculations.map((calculation) => {
                    const updatedValues = getUpdatedCalculationValues(calculation)

                    return (
                      <div key={calculation.id} className="bg-gray-700/50 rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h3 className="text-xl font-bold text-white">{calculation.name}</h3>
                            <p className="text-sm text-gray-400">
                              {dict.calculator.created}: {new Date(calculation.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              onClick={() => loadCalculation(calculation)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              {dict.calculator.load}
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => deleteCalculation(calculation.id)}
                              className="border-red-500 text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="grid md:grid-cols-3 gap-6">
                          {/* Your Offer Summary */}
                          <div className="bg-gray-800/50 rounded-lg p-4">
                            <h4 className="text-green-400 font-semibold mb-3">{dict.calculator.yourOffer}</h4>
                            <div className="space-y-2">
                              {updatedValues.yourFruits.map((fruit) => (
                                <div key={fruit.name} className="flex items-center justify-between text-sm">
                                  <span className="text-white">
                                    {fruit.name} x{fruit.quantity}
                                  </span>
                                  <span className="text-green-400">{formatValue(fruit.value * fruit.quantity)}</span>
                                </div>
                              ))}
                              <div className="border-t border-gray-600 pt-2 mt-2">
                                <div className="flex justify-between font-semibold">
                                  <span className="text-gray-300">{dict.calculator.totalValue}:</span>
                                  <span className="text-green-400">{formatValue(updatedValues.yourTotal)}</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Their Offer Summary */}
                          <div className="bg-gray-800/50 rounded-lg p-4">
                            <h4 className="text-purple-400 font-semibold mb-3">{dict.calculator.theirOffer}</h4>
                            <div className="space-y-2">
                              {updatedValues.theirFruits.map((fruit) => (
                                <div key={fruit.name} className="flex items-center justify-between text-sm">
                                  <span className="text-white">
                                    {fruit.name} x{fruit.quantity}
                                  </span>
                                  <span className="text-purple-400">{formatValue(fruit.value * fruit.quantity)}</span>
                                </div>
                              ))}
                              <div className="border-t border-gray-600 pt-2 mt-2">
                                <div className="flex justify-between font-semibold">
                                  <span className="text-gray-300">{dict.calculator.totalValue}:</span>
                                  <span className="text-purple-400">{formatValue(updatedValues.theirTotal)}</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Trade Analysis */}
                          <div className="bg-gray-800/50 rounded-lg p-4">
                            <h4 className="text-yellow-400 font-semibold mb-3 flex items-center gap-2">
                              {dict.calculator.tradeAnalysis}
                              {updatedValues.analysis.winner === "you" && (
                                <TrendingUp className="w-4 h-4 text-green-400" />
                              )}
                              {updatedValues.analysis.winner === "them" && (
                                <TrendingDown className="w-4 h-4 text-red-400" />
                              )}
                            </h4>
                            <div className="space-y-3">
                              <div className="text-center">
                                <div
                                  className={`text-2xl font-bold ${updatedValues.analysis.difference < 0 ? "text-red-400" : updatedValues.analysis.difference > 0 ? "text-green-400" : "text-gray-400"}`}
                                >
                                  {updatedValues.analysis.difference === 0
                                    ? "±"
                                    : updatedValues.analysis.difference < 0
                                      ? "-"
                                      : "+"}
                                  {formatValue(Math.abs(updatedValues.analysis.difference))}
                                </div>
                                <div className="text-xs text-gray-400">{dict.calculator.valueDifference}</div>
                              </div>
                              <div className="text-center">
                                <div className="text-xl font-bold text-yellow-400">
                                  {updatedValues.analysis.advantagePercent.toFixed(1)}%
                                </div>
                                <div className="text-xs text-gray-400">{dict.calculator.advantage}</div>
                              </div>
                              <div className="text-center">
                                <div className={`text-lg font-bold ${updatedValues.analysis.rating.color}`}>
                                  {updatedValues.analysis.rating.rating}
                                </div>
                                <div className="text-xs text-gray-400">{dict.calculator.tradeRating}</div>
                              </div>
                              <div className="text-center">
                                <div
                                  className={`text-sm font-bold ${updatedValues.analysis.winner === "you" ? "text-green-400" : updatedValues.analysis.winner === "them" ? "text-red-400" : "text-gray-400"}`}
                                >
                                  {updatedValues.analysis.advantageText}
                                </div>
                                <div className="text-xs text-gray-400">{dict.calculator.result}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Fruit Selector Modal */}
        <Dialog open={showFruitSelector} onOpenChange={setShowFruitSelector}>
          <DialogContent className="max-w-4xl bg-gray-800 border-gray-700 text-white">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold text-center">{dict.calculator.selectFruit}</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-3 gap-4 max-h-[500px] overflow-y-auto p-4">
              {allFruits.map((fruit) => (
                <Button
                  key={fruit.name}
                  onClick={() => addFruit(fruit)}
                  className="h-auto p-4 bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600 flex flex-col items-center space-y-2"
                  variant="outline"
                >
                  <div className="w-16 h-16 rounded-lg bg-gray-600 flex items-center justify-center overflow-hidden">
                    <img
                      src={fruit.image || "/placeholder.svg"}
                      alt={fruit.name}
                      className="w-14 h-14 object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement
                        target.src = "/placeholder.svg?height=56&width=56"
                      }}
                    />
                  </div>
                  <div className="text-center">
                    <div className="text-white font-semibold text-sm">{fruit.name}</div>
                    <div className="text-green-400 text-xs">{formatValue(fruit.value)}</div>
                  </div>
                </Button>
              ))}
            </div>
          </DialogContent>
        </Dialog>

        {/* Save Dialog */}
        <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
          <DialogContent className="bg-gray-800 border-gray-700 text-white">
            <DialogHeader>
              <DialogTitle className="text-xl font-bold">{dict.calculator.saveCalculation}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="save-name" className="text-white">
                  {dict.calculator.calculationName}
                </Label>
                <Input
                  id="save-name"
                  value={saveName}
                  onChange={(e) => setSaveName(e.target.value)}
                  placeholder={dict.calculator.enterName}
                  className="bg-gray-700 border-gray-600 text-white mt-2"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowSaveDialog(false)}
                  className="border-gray-600 text-white"
                >
                  {dict.calculator.cancel}
                </Button>
                <Button
                  onClick={saveCalculation}
                  disabled={!saveName.trim()}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {dict.calculator.save}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
