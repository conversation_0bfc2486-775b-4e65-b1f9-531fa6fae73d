"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { TrendingUp, TrendingDown, Users, BarChart3, Package, Clock, Calendar, AlertCircle } from "lucide-react"
import type { MarketData } from "@/lib/fruits-data"

interface MarketDashboardProps {
  dict: any
}

export function MarketDashboard({ dict }: MarketDashboardProps) {
  const [marketData, setMarketData] = useState<MarketData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        const response = await fetch("/api/fruits")
        if (response.ok) {
          const data = await response.json()
          setMarketData(data.marketData)
        }
      } catch (error) {
        console.error("Error fetching market data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchMarketData()
    const interval = setInterval(fetchMarketData, 2 * 60 * 1000) // Update every 2 minutes
    return () => clearInterval(interval)
  }, [])

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="text-white">Loading market data...</div>
      </div>
    )
  }

  if (!marketData) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-400">Market data unavailable</div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Market Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
          <CardContent className="p-4 text-center">
            <BarChart3 className="w-8 h-8 mx-auto mb-2 text-blue-400" />
            <div className="text-2xl font-bold text-white">{marketData.totalTrades.toLocaleString()}</div>
            <div className="text-sm text-gray-400">Total Trades</div>
          </CardContent>
        </Card>

        <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
          <CardContent className="p-4 text-center">
            <Users className="w-8 h-8 mx-auto mb-2 text-green-400" />
            <div className="text-2xl font-bold text-white">{marketData.activeTraders.toLocaleString()}</div>
            <div className="text-sm text-gray-400">Active Traders</div>
          </CardContent>
        </Card>

        <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
          <CardContent className="p-4 text-center">
            <Package className="w-8 h-8 mx-auto mb-2 text-purple-400" />
            <div className="text-2xl font-bold text-white">{marketData.serverStatus.players.toLocaleString()}</div>
            <div className="text-sm text-gray-400">Online Players</div>
          </CardContent>
        </Card>

        <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
          <CardContent className="p-4 text-center">
            <Clock className="w-8 h-8 mx-auto mb-2 text-yellow-400" />
            <div className="text-2xl font-bold text-white">{marketData.serverStatus.online ? "Online" : "Offline"}</div>
            <div className="text-sm text-gray-400">Server Status</div>
          </CardContent>
        </Card>
      </div>

      {/* Market Trends */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-green-400 flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Rising Fruits
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {marketData.marketTrends.rising.map((fruitName, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <span className="text-white font-medium">{fruitName}</span>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-green-500/20 text-green-400 border-green-500/50">Rising</Badge>
                    <TrendingUp className="w-4 h-4 text-green-400" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-red-400 flex items-center">
              <TrendingDown className="w-5 h-5 mr-2" />
              Falling Fruits
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {marketData.marketTrends.falling.map((fruitName, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <span className="text-white font-medium">{fruitName}</span>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-red-500/20 text-red-400 border-red-500/50">Falling</Badge>
                    <TrendingDown className="w-4 h-4 text-red-400" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Traded Fruits */}
      <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <BarChart3 className="w-5 h-5 mr-2" />
            Most Traded Fruits
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {marketData.topTradedFruits.map((fruitName, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <div className="text-white font-medium">{fruitName}</div>
                  <Progress value={100 - index * 20} className="h-2 mt-1" />
                </div>
                <Badge variant="outline" className="border-white/20 text-white">
                  #{index + 1}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Active Events */}
      {marketData.events && marketData.events.length > 0 && (
        <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-yellow-400 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Active Events
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {marketData.events.map((event, index) => (
                <div key={index} className="p-4 bg-white/5 rounded-lg border border-yellow-500/20">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="w-5 h-5 text-yellow-400 mt-0.5" />
                    <div className="flex-1">
                      <h4 className="text-white font-semibold mb-1">{event.name}</h4>
                      <p className="text-gray-400 text-sm mb-2">{event.description}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>Start: {new Date(event.startDate).toLocaleDateString()}</span>
                        <span>End: {new Date(event.endDate).toLocaleDateString()}</span>
                      </div>
                      <div className="mt-2">
                        <div className="text-xs text-gray-400 mb-1">Affected Fruits:</div>
                        <div className="flex flex-wrap gap-1">
                          {event.affectedFruits.map((fruit, fruitIndex) => (
                            <Badge
                              key={fruitIndex}
                              variant="outline"
                              className="border-yellow-500/50 text-yellow-400 text-xs"
                            >
                              {fruit}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
