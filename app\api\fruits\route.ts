import { NextResponse } from "next/server"

const RAPIDAPI_KEY = "**************************************************"
const RAPIDAPI_HOST = "blox-fruit-stock-fruit.p.rapidapi.com"

// Cache for 1 minute
let cachedData: any = null
let lastFetch = 0
const CACHE_DURATION = 1 * 60 * 1000

// Enhanced mock data with additional information
const mockFruitsData = {
  fruits: [
    {
      name: "Rocket",
      rarity: "Common",
      type: "Paramecia",
      value: 5000,
      awakened: false,
      stock: 85,
      demand: "Low",
      trend: "Stable",
      lastSeen: "2024-01-15T10:30:00Z",
      spawnRate: 15.5,
      popularTrades: ["Spin + Rocket", "Bomb + Rocket"],
      dealerStock: 12,
      cousinStock: 8,
      spawnLocation: ["Starter Island", "Marine Base"],
      tradingVolume: 245,
      priceChange24h: 0,
      priceChangePercent: 0,
      priceHistory: [
        { date: "2024-01-10", value: 5000 },
        { date: "2024-01-11", value: 5000 },
        { date: "2024-01-12", value: 5000 },
      ],
    },
    {
      name: "<PERSON><PERSON>",
      rarity: "Mythical",
      type: "Zoan",
      value: 5000000,
      awakened: true,
      stock: 2,
      demand: "Very High",
      trend: "Rising",
      lastSeen: "2024-01-15T14:22:00Z",
      spawnRate: 0.1,
      popularTrades: ["Dragon + Leopard", "Control + Leopard"],
      dealerStock: 0,
      cousinStock: 1,
      spawnLocation: ["Third Sea"],
      tradingVolume: 1250,
      priceChange24h: 200000,
      priceChangePercent: 4.2,
      priceHistory: [
        { date: "2024-01-10", value: 4800000 },
        { date: "2024-01-11", value: 4900000 },
        { date: "2024-01-12", value: 5000000 },
      ],
    },
    {
      name: "Dragon",
      rarity: "Mythical",
      type: "Zoan",
      value: 3500000,
      awakened: true,
      stock: 5,
      demand: "High",
      trend: "Stable",
      lastSeen: "2024-01-15T12:15:00Z",
      spawnRate: 0.2,
      popularTrades: ["Venom + Dragon", "Control + Dragon"],
      dealerStock: 1,
      cousinStock: 2,
      spawnLocation: ["Third Sea"],
      tradingVolume: 890,
      priceChange24h: -50000,
      priceChangePercent: -1.4,
    },
    // ... autres fruits avec données enrichies
  ],
  marketData: {
    totalTrades: 15420,
    activeTraders: 2341,
    topTradedFruits: ["Leopard", "Dragon", "Venom", "Control", "Dough"],
    marketTrends: {
      rising: ["Leopard", "Kitsune", "Spirit"],
      falling: ["Buddha", "Phoenix", "Rumble"],
    },
    serverStatus: {
      online: true,
      players: 12450,
      lastUpdate: new Date().toISOString(),
    },
    events: [
      {
        name: "Double XP Weekend",
        description: "All fruits give double XP for mastery",
        startDate: "2024-01-20T00:00:00Z",
        endDate: "2024-01-22T23:59:59Z",
        affectedFruits: ["All"],
      },
      {
        name: "Mythical Spawn Boost",
        description: "Increased spawn rate for mythical fruits",
        startDate: "2024-01-18T00:00:00Z",
        endDate: "2024-01-25T23:59:59Z",
        affectedFruits: ["Leopard", "Dragon", "Venom", "Control", "Spirit"],
      },
    ],
  },
  lastUpdated: new Date().toISOString(),
  status: "success",
}

export async function GET() {
  try {
    const now = Date.now()

    // Return cached data if it's still fresh
    if (cachedData && now - lastFetch < CACHE_DURATION) {
      return NextResponse.json({
        ...cachedData,
        cached: true,
        cacheAge: Math.floor((now - lastFetch) / 1000),
      })
    }

    // Try to fetch from RapidAPI
    let apiData = null
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000)

      const response = await fetch("https://blox-fruit-stock-fruit.p.rapidapi.com/", {
        method: "GET",
        headers: {
          "x-rapidapi-key": RAPIDAPI_KEY,
          "x-rapidapi-host": RAPIDAPI_HOST,
        },
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (response.ok) {
        const responseText = await response.text()
        try {
          apiData = JSON.parse(responseText)
        } catch (parseError) {
          console.log("Failed to parse API response as JSON:", parseError)
          apiData = null
        }
      }
    } catch (fetchError) {
      console.log("API fetch failed:", fetchError)
    }

    // Prepare response data with enhanced information
    let responseData
    if (apiData && apiData.fruits) {
      // Enhance API data with mock additional information
      responseData = {
        ...apiData,
        marketData: mockFruitsData.marketData,
        lastUpdated: new Date().toISOString(),
        source: "rapidapi_enhanced",
      }
    } else {
      // Use enhanced mock data as fallback
      responseData = {
        ...mockFruitsData,
        source: "mock_enhanced",
        note: "Using enhanced mock data - API unavailable",
      }
    }

    // Update cache
    cachedData = responseData
    lastFetch = now

    return NextResponse.json(responseData)
  } catch (error) {
    console.error("API route error:", error)

    const errorResponse = {
      ...mockFruitsData,
      source: "emergency_fallback",
      error: "API temporarily unavailable",
      errorDetails: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    }

    return NextResponse.json(errorResponse, { status: 200 })
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  })
}
