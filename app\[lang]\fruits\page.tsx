import { getDictionary } from "../dictionaries"
import { FruitsList } from "@/components/fruits-list"

export default async function FruitsPage({
  params,
}: {
  params: Promise<{ lang: string }>
}) {
  const { lang } = await params
  const dict = await getDictionary(lang as any)

  return (
    <div className="container mx-auto px-4 py-20">
      <FruitsList dict={dict} lang={lang} />
    </div>
  )
}
