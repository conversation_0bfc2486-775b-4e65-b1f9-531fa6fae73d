import { Card, CardContent } from "@/components/ui/card"
import { fruitsData } from "@/lib/fruits-data"
import { TrendingUp, Star, Zap, Crown } from "lucide-react"

interface StatsSectionProps {
  dict: any
}

export function StatsSection({ dict }: StatsSectionProps) {
  const totalFruits = fruitsData.length // Maintenant 54 fruits
  const mythicalFruits = fruitsData.filter((f) => f.rarity === "Mythical").length
  const awakenedFruits = fruitsData.filter((f) => f.awakened).length
  const highestValue = Math.max(...fruitsData.map((f) => f.value))

  const stats = [
    {
      icon: TrendingUp,
      value: totalFruits,
      label: dict.stats.totalFruits,
      color: "text-blue-400",
    },
    {
      icon: Crown,
      value: mythicalFruits,
      label: dict.stats.mythicalFruits,
      color: "text-purple-400",
    },
    {
      icon: Star,
      value: awakenedFruits,
      label: dict.stats.awakenedFruits,
      color: "text-yellow-400",
    },
    {
      icon: Zap,
      value: `$${(highestValue / 1000000).toFixed(1)}M`,
      label: dict.stats.highestValue,
      color: "text-green-400",
    },
  ]

  return (
    <section className="py-20 px-4 bg-black/50">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-white mb-4">{dict.stats.title}</h2>
          <p className="text-xl text-gray-400">{dict.stats.subtitle}</p>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card
              key={index}
              className="bg-white/5 border-white/10 backdrop-blur-sm hover:bg-white/10 transition-all duration-300"
            >
              <CardContent className="p-6 text-center">
                <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-white/10 flex items-center justify-center`}>
                  <stat.icon className={`w-8 h-8 ${stat.color}`} />
                </div>
                <div className={`text-3xl font-bold mb-2 ${stat.color}`}>{stat.value}</div>
                <div className="text-gray-400 text-sm">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
