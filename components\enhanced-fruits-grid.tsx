"use client"

import { useState, useMemo, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import {
  Search,
  Star,
  ExternalLink,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus,
  Package,
  Clock,
  Users,
  BarChart3,
} from "lucide-react"
import {
  fruitsData,
  getRarityColor,
  getTypeColor,
  getDemandColor,
  type ApiFruit,
  type MarketData,
  mergeFruitData,
} from "@/lib/fruits-data"

interface EnhancedFruitsGridProps {
  dict: any
  lang: string
}

export function EnhancedFruitsGrid({ dict, lang }: EnhancedFruitsGridProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [rarityFilter, setRarityFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [demandFilter, setDemandFilter] = useState("all")
  const [sortBy, setSortBy] = useState("name")
  const [apiData, setApiData] = useState<ApiFruit[]>([])
  const [marketData, setMarketData] = useState<MarketData | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<string>("")

  // Fetch data from API
  const fetchApiData = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/fruits")
      if (response.ok) {
        const data = await response.json()
        setApiData(data.fruits || [])
        setMarketData(data.marketData || null)
        setLastUpdated(data.lastUpdated || new Date().toISOString())
      }
    } catch (error) {
      console.error("Error fetching API data:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchApiData()
    const interval = setInterval(fetchApiData, 1 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  // Merge static data with API data
  const mergedFruits = useMemo(() => {
    return fruitsData.map((staticFruit) => {
      const apiMatch = apiData.find((api) => api.name.toLowerCase() === staticFruit.name.toLowerCase())
      return mergeFruitData(staticFruit, apiMatch)
    })
  }, [apiData])

  const filteredAndSortedFruits = useMemo(() => {
    const filtered = mergedFruits.filter((fruit) => {
      const matchesSearch = fruit.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesRarity = rarityFilter === "all" || fruit.rarity.toLowerCase() === rarityFilter
      const matchesType = typeFilter === "all" || fruit.type.toLowerCase() === typeFilter
      const matchesDemand = demandFilter === "all" || fruit.demand?.toLowerCase() === demandFilter

      return matchesSearch && matchesRarity && matchesType && matchesDemand
    })

    // Sort fruits
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "value":
          return b.value - a.value
        case "demand":
          const demandOrder = { "Very High": 4, High: 3, Medium: 2, Low: 1 }
          return (
            (demandOrder[b.demand as keyof typeof demandOrder] || 0) -
            (demandOrder[a.demand as keyof typeof demandOrder] || 0)
          )
        case "stock":
          return (b.stock || 0) - (a.stock || 0)
        case "rarity":
          const rarityOrder = { Common: 1, Uncommon: 2, Rare: 3, Legendary: 4, Mythical: 5 }
          return rarityOrder[b.rarity as keyof typeof rarityOrder] - rarityOrder[a.rarity as keyof typeof rarityOrder]
        case "name":
        default:
          return a.name.localeCompare(b.name)
      }
    })

    return filtered
  }, [mergedFruits, searchTerm, rarityFilter, typeFilter, demandFilter, sortBy])

  return (
    <section id="fruits" className="py-20 px-4 bg-gradient-to-b from-gray-900 to-black">
      <div className="container mx-auto max-w-7xl">
        <div className="text-center mb-12">
          <h2 className="text-5xl font-bold text-white mb-4">{dict.fruits.title}</h2>
          <p className="text-xl text-gray-400 mb-4">{dict.fruits.subtitle}</p>

          {/* Market Overview */}
          {marketData && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 max-w-4xl mx-auto">
              <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                <CardContent className="p-4 text-center">
                  <Users className="w-6 h-6 mx-auto mb-2 text-blue-400" />
                  <div className="text-lg font-bold text-white">{marketData.totalTrades}</div>
                  <div className="text-xs text-gray-400">Total Trades</div>
                </CardContent>
              </Card>
              <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                <CardContent className="p-4 text-center">
                  <BarChart3 className="w-6 h-6 mx-auto mb-2 text-green-400" />
                  <div className="text-lg font-bold text-white">{marketData.activeTraders}</div>
                  <div className="text-xs text-gray-400">Active Traders</div>
                </CardContent>
              </Card>
              <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                <CardContent className="p-4 text-center">
                  <Package className="w-6 h-6 mx-auto mb-2 text-purple-400" />
                  <div className="text-lg font-bold text-white">{marketData.serverStatus.players}</div>
                  <div className="text-xs text-gray-400">Online Players</div>
                </CardContent>
              </Card>
              <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                <CardContent className="p-4 text-center">
                  <Clock className="w-6 h-6 mx-auto mb-2 text-yellow-400" />
                  <div className="text-lg font-bold text-white">
                    {marketData.serverStatus.online ? "Online" : "Offline"}
                  </div>
                  <div className="text-xs text-gray-400">Server Status</div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* API Status */}
          <div className="flex items-center justify-center gap-4 mb-4">
            <Badge variant="outline" className="border-green-500/50 text-green-400">
              {loading ? "Updating..." : "Live Data"}
            </Badge>
            {lastUpdated && (
              <span className="text-sm text-gray-500">Last updated: {new Date(lastUpdated).toLocaleTimeString()}</span>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={fetchApiData}
              disabled={loading}
              className="border-white/20 text-white hover:bg-white/10"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Enhanced Filters */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder={dict.fruits.search}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            />
          </div>

          <Select value={rarityFilter} onValueChange={setRarityFilter}>
            <SelectTrigger className="bg-white/10 border-white/20 text-white">
              <SelectValue placeholder={dict.fruits.filterRarity} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{dict.fruits.allRarities}</SelectItem>
              <SelectItem value="common">Common</SelectItem>
              <SelectItem value="uncommon">Uncommon</SelectItem>
              <SelectItem value="rare">Rare</SelectItem>
              <SelectItem value="legendary">Legendary</SelectItem>
              <SelectItem value="mythical">Mythical</SelectItem>
            </SelectContent>
          </Select>

          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="bg-white/10 border-white/20 text-white">
              <SelectValue placeholder={dict.fruits.filterType} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{dict.fruits.allTypes}</SelectItem>
              <SelectItem value="paramecia">Paramecia</SelectItem>
              <SelectItem value="logia">Logia</SelectItem>
              <SelectItem value="zoan">Zoan</SelectItem>
            </SelectContent>
          </Select>

          <Select value={demandFilter} onValueChange={setDemandFilter}>
            <SelectTrigger className="bg-white/10 border-white/20 text-white">
              <SelectValue placeholder="Filter by Demand" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Demands</SelectItem>
              <SelectItem value="very high">Very High</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="bg-white/10 border-white/20 text-white">
              <SelectValue placeholder={dict.fruits.sortBy} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">{dict.fruits.sortName}</SelectItem>
              <SelectItem value="value">{dict.fruits.sortValue}</SelectItem>
              <SelectItem value="rarity">{dict.fruits.sortRarity}</SelectItem>
              <SelectItem value="demand">Demand</SelectItem>
              <SelectItem value="stock">Stock</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Results count */}
        <div className="mb-6">
          <p className="text-gray-400">
            {dict.fruits.showing} {filteredAndSortedFruits.length} {dict.fruits.results}
          </p>
        </div>

        {/* Enhanced Fruits Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredAndSortedFruits.map((fruit) => (
            <Card
              key={fruit.id}
              className="bg-white/5 border-white/10 backdrop-blur-sm hover:bg-white/10 transition-all duration-300 group overflow-hidden"
            >
              <CardContent className="p-0">
                <div className="relative">
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${getRarityColor(fruit.rarity)} opacity-20`}
                  ></div>
                  <div className="relative p-6">
                    {/* Fruit Image */}
                    <div className="relative mb-4 flex justify-center">
                      <div className="w-24 h-24 rounded-full bg-white/10 flex items-center justify-center overflow-hidden">
                        <img
                          src={fruit.image || "/placeholder.svg"}
                          alt={fruit.name}
                          className="w-20 h-20 object-contain"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = "/placeholder.svg?height=80&width=80"
                          }}
                        />
                      </div>
                      {fruit.awakened && (
                        <div className="absolute -top-2 -right-2">
                          <Star className="w-6 h-6 text-yellow-400 fill-current" />
                        </div>
                      )}
                      {fruit.trend && (
                        <div className="absolute -top-2 -left-2">
                          {fruit.trend === "Rising" ? (
                            <TrendingUp className="w-5 h-5 text-green-400" />
                          ) : fruit.trend === "Falling" ? (
                            <TrendingDown className="w-5 h-5 text-red-400" />
                          ) : (
                            <Minus className="w-5 h-5 text-gray-400" />
                          )}
                        </div>
                      )}
                    </div>

                    {/* Fruit Info */}
                    <div className="text-center mb-4">
                      <h3 className="text-xl font-bold text-white mb-2">{fruit.name}</h3>
                      <div className="flex justify-center gap-2 mb-3 flex-wrap">
                        <Badge className={`${getRarityColor(fruit.rarity)} bg-gradient-to-r text-white border-0`}>
                          {fruit.rarity}
                        </Badge>
                        <Badge className={`${getTypeColor(fruit.type)} text-white`}>{fruit.type}</Badge>
                        {fruit.demand && (
                          <Badge className={`${getDemandColor(fruit.demand)} border-0`}>{fruit.demand}</Badge>
                        )}
                      </div>
                    </div>

                    {/* Enhanced Value Section */}
                    <div className="text-center mb-4">
                      <div className="flex items-center justify-center gap-2">
                        <div className="text-2xl font-bold text-green-400">${fruit.value.toLocaleString()}</div>
                        {apiData.some((api) => api.name.toLowerCase() === fruit.name.toLowerCase()) && (
                          <Badge variant="outline" className="border-green-500/50 text-green-400 text-xs">
                            LIVE
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-gray-400">{fruit.robux.toLocaleString()} Robux</div>

                      {/* Stock Information */}
                      {fruit.stock !== undefined && (
                        <div className="mt-2">
                          <div className="flex items-center justify-between text-xs text-gray-400 mb-1">
                            <span>Stock</span>
                            <span>{fruit.stock}/100</span>
                          </div>
                          <Progress value={fruit.stock} className="h-1" />
                        </div>
                      )}
                    </div>

                    {/* Additional Info */}
                    {(fruit.lastSeen || fruit.spawnRate) && (
                      <div className="text-xs text-gray-400 mb-4 space-y-1">
                        {fruit.lastSeen && (
                          <div className="flex items-center justify-between">
                            <span>Last Seen:</span>
                            <span>{new Date(fruit.lastSeen).toLocaleDateString()}</span>
                          </div>
                        )}
                        {fruit.spawnRate && (
                          <div className="flex items-center justify-between">
                            <span>Spawn Rate:</span>
                            <span>{fruit.spawnRate}%</span>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button
                        asChild
                        className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                      >
                        <Link href={`/${lang === "en" ? "" : lang}/fruit/${fruit.id}`}>{dict.fruits.viewDetails}</Link>
                      </Button>
                      <Button
                        asChild
                        variant="outline"
                        size="sm"
                        className="border-white/20 text-white hover:bg-white/10"
                      >
                        <a href={fruit.wikiUrl} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredAndSortedFruits.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-400 text-xl">{dict.fruits.noResults}</p>
          </div>
        )}

        {/* Market Trends Section */}
        {marketData && (
          <div className="mt-16">
            <h3 className="text-3xl font-bold text-white mb-8 text-center">Market Trends</h3>
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                <CardContent className="p-6">
                  <h4 className="text-xl font-bold text-green-400 mb-4 flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2" />
                    Rising Fruits
                  </h4>
                  <div className="space-y-2">
                    {marketData.marketTrends.rising.map((fruitName, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-white/5 rounded">
                        <span className="text-white">{fruitName}</span>
                        <TrendingUp className="w-4 h-4 text-green-400" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                <CardContent className="p-6">
                  <h4 className="text-xl font-bold text-red-400 mb-4 flex items-center">
                    <TrendingDown className="w-5 h-5 mr-2" />
                    Falling Fruits
                  </h4>
                  <div className="space-y-2">
                    {marketData.marketTrends.falling.map((fruitName, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-white/5 rounded">
                        <span className="text-white">{fruitName}</span>
                        <TrendingDown className="w-4 h-4 text-red-400" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
