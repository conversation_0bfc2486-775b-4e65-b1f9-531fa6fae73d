export interface Fruit {
  id: string
  name: string
  image: string
  wikiUrl: string
  rarity: "Common" | "Uncommon" | "Rare" | "Legendary" | "Mythical"
  type: "Paramecia" | "Logia" | "Zoan"
  value: number
  awakened: boolean
  robux: number
  description: string
  abilities: string[]
  pros: string[]
  cons: string[]
  mastery: {
    level: number
    requirement: string
  }[]
  // Nouvelles propriétés pour les données API
  stock?: number
  demand?: "Low" | "Medium" | "High" | "Very High"
  trend?: "Rising" | "Falling" | "Stable"
  lastSeen?: string
  spawnRate?: number
  popularTrades?: string[]
  priceHistory?: {
    date: string
    value: number
  }[]
}

// Interface étendue pour les données de l'API
export interface ApiFruit {
  name: string
  value: number
  rarity: string
  type: string
  awakened: boolean
  // Nouvelles données potentielles de l'API
  stock?: number
  demand?: string
  trend?: string
  lastSeen?: string
  spawnRate?: number
  popularTrades?: string[]
  priceHistory?: {
    date: string
    value: number
  }[]
  dealerStock?: number
  cousinStock?: number
  spawnLocation?: string[]
  tradingVolume?: number
  priceChange24h?: number
  priceChangePercent?: number
}

// Interface pour les données du marché
export interface MarketData {
  totalTrades: number
  activeTraders: number
  topTradedFruits: string[]
  marketTrends: {
    rising: string[]
    falling: string[]
  }
  serverStatus: {
    online: boolean
    players: number
    lastUpdate: string
  }
  events?: {
    name: string
    description: string
    startDate: string
    endDate: string
    affectedFruits: string[]
  }[]
}

export const fruitsData: Fruit[] = [
  {
    id: "rocket",
    name: "Rocket",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/d4/RocketFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Rocket",
    rarity: "Common",
    type: "Paramecia",
    value: 5000,
    awakened: false,
    robux: 50,
    description: "The Rocket fruit allows the user to launch explosive rockets at enemies.",
    abilities: ["Rocket", "Bazooka", "Missile"],
    pros: ["Good for grinding", "Area damage", "Long range"],
    cons: ["Low damage", "Slow projectiles", "Not good for PvP"],
    mastery: [
      { level: 1, requirement: "Rocket" },
      { level: 50, requirement: "Bazooka" },
      { level: 100, requirement: "Missile" },
    ],
  },
  {
    id: "spin",
    name: "Spin",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/dc/SpinFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Spin",
    rarity: "Common",
    type: "Paramecia",
    value: 7500,
    awakened: false,
    robux: 75,
    description: "The Spin fruit allows the user to spin and create tornadoes.",
    abilities: ["Spin", "Tornado", "Hurricane"],
    pros: ["Good mobility", "Area damage", "Stun effects"],
    cons: ["Low damage", "Hard to aim", "Not good for bosses"],
    mastery: [
      { level: 1, requirement: "Spin" },
      { level: 75, requirement: "Tornado" },
      { level: 150, requirement: "Hurricane" },
    ],
  },
  {
    id: "blade",
    name: "Blade",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2a/BladeFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Blade",
    rarity: "Common",
    type: "Paramecia",
    value: 15000,
    awakened: false,
    robux: 150,
    description: "The Blade fruit allows the user to create and control sharp blades.",
    abilities: ["Blade Rush", "Sword Tornado", "Blade Prison"],
    pros: ["High damage", "Good combo potential", "Fast attacks"],
    cons: ["Short range", "No logia immunity", "Requires skill"],
    mastery: [
      { level: 1, requirement: "Blade Rush" },
      { level: 100, requirement: "Sword Tornado" },
      { level: 200, requirement: "Blade Prison" },
    ],
  },
  {
    id: "spring",
    name: "Spring",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/46/SpringFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Spring",
    rarity: "Common",
    type: "Paramecia",
    value: 60000,
    awakened: false,
    robux: 600,
    description: "The Spring fruit allows the user to bounce and spring around.",
    abilities: ["Spring Leap", "Spring Cannon", "Spring Emperor"],
    pros: ["Great mobility", "Good for escaping", "Unique movement"],
    cons: ["Low damage", "Hard to control", "Not good for grinding"],
    mastery: [
      { level: 1, requirement: "Spring Leap" },
      { level: 50, requirement: "Spring Cannon" },
      { level: 100, requirement: "Spring Emperor" },
    ],
  },
  {
    id: "bomb",
    name: "Bomb",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/7d/BombFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Bomb",
    rarity: "Common",
    type: "Paramecia",
    value: 80000,
    awakened: false,
    robux: 800,
    description: "The Bomb fruit allows the user to create and detonate explosives.",
    abilities: ["Bomb Grab", "Bomb Cannon", "Self Destruct"],
    pros: ["Area damage", "Good for grinding", "High knockback"],
    cons: ["Self damage", "Slow attacks", "Limited range"],
    mastery: [
      { level: 1, requirement: "Bomb Grab" },
      { level: 100, requirement: "Bomb Cannon" },
      { level: 200, requirement: "Self Destruct" },
    ],
  },
  {
    id: "smoke",
    name: "Smoke",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/68/SmokeFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Smoke",
    rarity: "Common",
    type: "Logia",
    value: 100000,
    awakened: false,
    robux: 1000,
    description: "The Smoke fruit is a Logia that allows the user to create and control smoke.",
    abilities: ["Smoke Bomber", "Smoke Liberation", "Smoke Blast"],
    pros: ["Logia immunity", "Good for grinding", "Area damage"],
    cons: ["Low damage", "Weak against higher levels", "Slow projectiles"],
    mastery: [
      { level: 1, requirement: "Smoke Bomber" },
      { level: 100, requirement: "Smoke Liberation" },
      { level: 200, requirement: "Smoke Blast" },
    ],
  },
  {
    id: "spike",
    name: "Spike",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2d/SpikeFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Spike",
    rarity: "Common",
    type: "Paramecia",
    value: 180000,
    awakened: false,
    robux: 1800,
    description: "The Spike fruit allows the user to create and control spikes.",
    abilities: ["Spike Summon", "Whirlwind", "Spike Barrage"],
    pros: ["Good damage", "Area control", "Good for grinding"],
    cons: ["Short range", "Predictable attacks", "No mobility"],
    mastery: [
      { level: 1, requirement: "Spike Summon" },
      { level: 100, requirement: "Whirlwind" },
      { level: 200, requirement: "Spike Barrage" },
    ],
  },
  {
    id: "flame",
    name: "Flame",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b8/FlameFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Flame",
    rarity: "Uncommon",
    type: "Logia",
    value: 250000,
    awakened: true,
    robux: 2500,
    description: "The Flame fruit is a Logia that allows the user to create and control fire.",
    abilities: ["Fire Bullets", "Burning Blast", "Fire Pillar", "Flame Destroyer"],
    pros: ["Logia immunity", "High damage", "Good for grinding", "Awakening available"],
    cons: ["Weak against water", "High mastery requirements", "Expensive awakening"],
    mastery: [
      { level: 1, requirement: "Fire Bullets" },
      { level: 100, requirement: "Burning Blast" },
      { level: 200, requirement: "Fire Pillar" },
      { level: 300, requirement: "Flame Destroyer" },
    ],
  },
  {
    id: "ice",
    name: "Ice",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/74/IceFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Ice",
    rarity: "Uncommon",
    type: "Logia",
    value: 350000,
    awakened: true,
    robux: 3500,
    description: "The Ice fruit is a Logia that allows the user to create and control ice.",
    abilities: ["Ice Spears", "Ice Surge", "Glacial Epoch", "Absolute Zero"],
    pros: ["Logia immunity", "Freeze effects", "Good damage", "Area control"],
    cons: ["Weak against fire", "Slow attacks", "High awakening cost"],
    mastery: [
      { level: 1, requirement: "Ice Spears" },
      { level: 100, requirement: "Ice Surge" },
      { level: 200, requirement: "Glacial Epoch" },
      { level: 300, requirement: "Absolute Zero" },
    ],
  },
  {
    id: "sand",
    name: "Sand",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/55/SandFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Sand",
    rarity: "Uncommon",
    type: "Logia",
    value: 420000,
    awakened: true,
    robux: 4200,
    description: "The Sand fruit is a Logia that allows the user to create and control sand.",
    abilities: ["Sand Tornado", "Heavy Sand", "Sandstorm", "Desert Blade"],
    pros: ["Logia immunity", "Good damage", "Area attacks", "Awakening available"],
    cons: ["Weak against water", "Slow projectiles", "High mastery requirements"],
    mastery: [
      { level: 1, requirement: "Sand Tornado" },
      { level: 100, requirement: "Heavy Sand" },
      { level: 200, requirement: "Sandstorm" },
      { level: 300, requirement: "Desert Blade" },
    ],
  },
  {
    id: "dark",
    name: "Dark",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/c/c2/DarkFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Dark",
    rarity: "Uncommon",
    type: "Logia",
    value: 500000,
    awakened: true,
    robux: 5000,
    description: "The Dark fruit is a Logia that allows the user to create and control darkness.",
    abilities: ["Zenitsu", "Dimensional Rift", "Dark Rocks", "Abyssal Darkness"],
    pros: ["Logia immunity", "High damage", "Good for PvP", "Unique abilities"],
    cons: ["No flight", "High skill requirement", "Expensive awakening"],
    mastery: [
      { level: 1, requirement: "Zenitsu" },
      { level: 100, requirement: "Dimensional Rift" },
      { level: 200, requirement: "Dark Rocks" },
      { level: 300, requirement: "Abyssal Darkness" },
    ],
  },
  {
    id: "light",
    name: "Light",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/f8/LightFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Light",
    rarity: "Rare",
    type: "Logia",
    value: 650000,
    awakened: true,
    robux: 6500,
    description: "The Light fruit is a Logia that allows the user to create and control light.",
    abilities: ["Light Kick", "Reflection Kick", "Sky Beam", "Divine Arrow"],
    pros: ["Logia immunity", "Fastest fruit", "High damage", "Great mobility"],
    cons: ["Hard to master", "Expensive", "High awakening cost"],
    mastery: [
      { level: 1, requirement: "Light Kick" },
      { level: 100, requirement: "Reflection Kick" },
      { level: 200, requirement: "Sky Beam" },
      { level: 300, requirement: "Divine Arrow" },
    ],
  },
  {
    id: "magma",
    name: "Magma",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/73/MagmaFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Magma",
    rarity: "Rare",
    type: "Logia",
    value: 850000,
    awakened: true,
    robux: 8500,
    description: "The Magma fruit is a Logia that allows the user to create and control magma.",
    abilities: ["Magma Fist", "Magma Rain", "Magma Meteor", "Volcanic Storm"],
    pros: ["Logia immunity", "Highest damage Logia", "Great for grinding", "Area damage"],
    cons: ["Slow attacks", "High mastery requirements", "Expensive awakening"],
    mastery: [
      { level: 1, requirement: "Magma Fist" },
      { level: 100, requirement: "Magma Rain" },
      { level: 200, requirement: "Magma Meteor" },
      { level: 300, requirement: "Volcanic Storm" },
    ],
  },
  {
    id: "buddha",
    name: "Buddha",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/6e/BuddhaFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Buddha",
    rarity: "Legendary",
    type: "Zoan",
    value: 1200000,
    awakened: true,
    robux: 12000,
    description: "The Buddha fruit is a Zoan that transforms the user into a giant golden Buddha.",
    abilities: ["Transform", "Impact", "Buddha Leap", "Retribution Dash"],
    pros: ["Huge size", "High damage reduction", "Great for grinding", "Tanky"],
    cons: ["Slow movement", "Big target", "Expensive awakening"],
    mastery: [
      { level: 1, requirement: "Transform" },
      { level: 100, requirement: "Impact" },
      { level: 200, requirement: "Buddha Leap" },
      { level: 300, requirement: "Retribution Dash" },
    ],
  },
  {
    id: "phoenix",
    name: "Phoenix",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/11/PhoenixFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Phoenix",
    rarity: "Legendary",
    type: "Zoan",
    value: 1800000,
    awakened: true,
    robux: 18000,
    description: "The Phoenix fruit is a Zoan that allows the user to transform into a phoenix.",
    abilities: ["Cannon", "Regeneration Flames", "Flame Exodus", "Blue Fire Bullets"],
    pros: ["Flight ability", "Regeneration", "Good damage", "Hybrid form"],
    cons: ["Hard to master", "Expensive", "High awakening cost"],
    mastery: [
      { level: 1, requirement: "Cannon" },
      { level: 100, requirement: "Regeneration Flames" },
      { level: 200, requirement: "Flame Exodus" },
      { level: 300, requirement: "Blue Fire Bullets" },
    ],
  },
  {
    id: "rumble",
    name: "Rumble",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/89/RumbleFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Rumble",
    rarity: "Legendary",
    type: "Logia",
    value: 2100000,
    awakened: true,
    robux: 21000,
    description: "The Rumble fruit is a Logia that allows the user to create and control lightning.",
    abilities: ["Thunder Clap", "Sky Thunder", "Thunderball Destruction", "Lightning Beast"],
    pros: ["Logia immunity", "High damage", "Great mobility", "Stun effects"],
    cons: ["High skill requirement", "Expensive", "Complex combos"],
    mastery: [
      { level: 1, requirement: "Thunder Clap" },
      { level: 100, requirement: "Sky Thunder" },
      { level: 200, requirement: "Thunderball Destruction" },
      { level: 300, requirement: "Lightning Beast" },
    ],
  },
  {
    id: "dough",
    name: "Dough",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/9f/DoughFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Dough",
    rarity: "Mythical",
    type: "Paramecia",
    value: 2800000,
    awakened: true,
    robux: 28000,
    description: "The Dough fruit is a Mythical Paramecia that allows the user to create and control dough.",
    abilities: ["Fried Dough", "Sticky Dough", "Carved Dough", "Restless Dough Barrage"],
    pros: ["Extremely high damage", "Great for PvP", "Unique mechanics", "Awakening available"],
    cons: ["Very expensive", "Hard to master", "High awakening cost"],
    mastery: [
      { level: 1, requirement: "Fried Dough" },
      { level: 100, requirement: "Sticky Dough" },
      { level: 200, requirement: "Carved Dough" },
      { level: 300, requirement: "Restless Dough Barrage" },
    ],
  },
  {
    id: "shadow",
    name: "Shadow",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2d/ShadowFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Shadow",
    rarity: "Mythical",
    type: "Paramecia",
    value: 2900000,
    awakened: true,
    robux: 29000,
    description: "The Shadow fruit is a Mythical Paramecia that allows the user to control shadows.",
    abilities: ["Shadow Grasp", "Corvus Torment", "Umbrage", "Nightmare Leech"],
    pros: ["Extremely high damage", "Great for PvP", "Unique abilities", "Life steal"],
    cons: ["Very expensive", "Hard to master", "Complex mechanics"],
    mastery: [
      { level: 1, requirement: "Shadow Grasp" },
      { level: 100, requirement: "Corvus Torment" },
      { level: 200, requirement: "Umbrage" },
      { level: 300, requirement: "Nightmare Leech" },
    ],
  },
  {
    id: "venom",
    name: "Venom",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b7/VenomFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Venom",
    rarity: "Mythical",
    type: "Logia",
    value: 3000000,
    awakened: true,
    robux: 30000,
    description: "The Venom fruit is a Mythical Logia that allows the user to create and control poison.",
    abilities: ["Poison Daggers", "Venom Pool", "Toxic Fog", "Transformation"],
    pros: ["Logia immunity", "Poison effects", "High damage", "Transformation"],
    cons: ["Very expensive", "Complex mechanics", "High skill requirement"],
    mastery: [
      { level: 1, requirement: "Poison Daggers" },
      { level: 100, requirement: "Venom Pool" },
      { level: 200, requirement: "Toxic Fog" },
      { level: 300, requirement: "Transformation" },
    ],
  },
  {
    id: "control",
    name: "Control",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/0/01/ControlFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Control",
    rarity: "Mythical",
    type: "Paramecia",
    value: 3200000,
    awakened: false,
    robux: 32000,
    description: "The Control fruit is a Mythical Paramecia that allows the user to control gravity.",
    abilities: ["Levitate", "Teleport", "Meteor Rain", "Gravity Push"],
    pros: ["Unique abilities", "Great mobility", "Area control", "High damage"],
    cons: ["Very expensive", "No awakening yet", "Complex mechanics"],
    mastery: [
      { level: 1, requirement: "Levitate" },
      { level: 100, requirement: "Teleport" },
      { level: 200, requirement: "Meteor Rain" },
      { level: 300, requirement: "Gravity Push" },
    ],
  },
  {
    id: "spirit",
    name: "Spirit",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/0/02/SpiritFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Spirit",
    rarity: "Mythical",
    type: "Paramecia",
    value: 3400000,
    awakened: false,
    robux: 34000,
    description: "The Spirit fruit is a Mythical Paramecia that allows the user to control souls.",
    abilities: ["Soul Guitar", "Frostfire Grasp", "Bone Chill", "Soul Snatcher"],
    pros: ["Extremely high damage", "Unique mechanics", "Great for PvP", "Soul effects"],
    cons: ["Very expensive", "No awakening yet", "Hard to obtain"],
    mastery: [
      { level: 1, requirement: "Soul Guitar" },
      { level: 100, requirement: "Frostfire Grasp" },
      { level: 200, requirement: "Bone Chill" },
      { level: 300, requirement: "Soul Snatcher" },
    ],
  },
  {
    id: "dragon",
    name: "Dragon",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/43/DragonFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Dragon",
    rarity: "Mythical",
    type: "Zoan",
    value: 3500000,
    awakened: true,
    robux: 35000,
    description: "The Dragon fruit is a Mythical Zoan that allows the user to transform into a dragon.",
    abilities: ["Dragon Rush", "Fire Shower", "Dragonic Claw", "Heatwave Beam"],
    pros: ["Highest value fruit", "Flight ability", "Transformation", "Extremely high damage"],
    cons: ["Most expensive", "Hard to obtain", "Complex mechanics"],
    mastery: [
      { level: 1, requirement: "Dragon Rush" },
      { level: 100, requirement: "Fire Shower" },
      { level: 200, requirement: "Dragonic Claw" },
      { level: 300, requirement: "Heatwave Beam" },
    ],
  },
  {
    id: "leopard",
    name: "Leopard",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/8c/LeopardFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Leopard",
    rarity: "Mythical",
    type: "Zoan",
    value: 5000000,
    awakened: true,
    robux: 50000,
    description: "The Leopard fruit is the most valuable Mythical Zoan that transforms the user into a leopard.",
    abilities: ["Finger Revolver", "Spiraling Kick", "Afterimage Assault", "Body Flicker"],
    pros: ["Highest value", "Incredible speed", "High damage", "Great for PvP"],
    cons: ["Most expensive fruit", "Extremely rare", "Hard to master"],
    mastery: [
      { level: 1, requirement: "Finger Revolver" },
      { level: 100, requirement: "Spiraling Kick" },
      { level: 200, requirement: "Afterimage Assault" },
      { level: 300, requirement: "Body Flicker" },
    ],
  },
  {
    id: "kitsune",
    name: "Kitsune",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/c/c3/KitsuneFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Kitsune",
    rarity: "Mythical",
    type: "Zoan",
    value: 4000000,
    awakened: false,
    robux: 40000,
    description: "The Kitsune fruit is a Mythical Zoan that allows the user to transform into a fox spirit.",
    abilities: ["Foxfire", "Spirit Dash", "Illusion", "Nine Tails"],
    pros: ["Unique abilities", "Great mobility", "Illusion effects", "High damage"],
    cons: ["Very expensive", "No awakening yet", "New fruit"],
    mastery: [
      { level: 1, requirement: "Foxfire" },
      { level: 100, requirement: "Spirit Dash" },
      { level: 200, requirement: "Illusion" },
      { level: 300, requirement: "Nine Tails" },
    ],
  },
  {
    id: "eagle",
    name: "Eagle",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/66/EagleFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Eagle",
    rarity: "Common",
    type: "Zoan",
    value: 300000,
    awakened: false,
    robux: 3000,
    description: "The Eagle fruit is a Zoan that allows the user to transform into an eagle.",
    abilities: ["Eagle Flight", "Talon Strike", "Wind Slash"],
    pros: ["Flight ability", "Good mobility", "Decent damage"],
    cons: ["Low health", "Weak against ranged attacks", "Limited ground combat"],
    mastery: [
      { level: 1, requirement: "Eagle Flight" },
      { level: 100, requirement: "Talon Strike" },
      { level: 200, requirement: "Wind Slash" },
    ],
  },
  {
    id: "diamond",
    name: "Diamond",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/6d/DiamondFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Diamond",
    rarity: "Uncommon",
    type: "Paramecia",
    value: 600000,
    awakened: false,
    robux: 6000,
    description: "The Diamond fruit allows the user to create and control diamonds.",
    abilities: ["Diamond Spikes", "Diamond Barrage", "Diamond Meteor"],
    pros: ["High defense", "Good damage", "Stunning effects"],
    cons: ["Slow attacks", "Limited mobility", "High mastery requirements"],
    mastery: [
      { level: 1, requirement: "Diamond Spikes" },
      { level: 100, requirement: "Diamond Barrage" },
      { level: 200, requirement: "Diamond Meteor" },
    ],
  },
  {
    id: "rubber",
    name: "Rubber",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/bc/RubberFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Rubber",
    rarity: "Rare",
    type: "Paramecia",
    value: 750000,
    awakened: true,
    robux: 7500,
    description: "The Rubber fruit allows the user to stretch their body like rubber.",
    abilities: ["Rubber Pistol", "Rubber Rocket", "Rubber Gatling", "Rubber Storm"],
    pros: ["Immunity to electric attacks", "Good mobility", "Awakening available"],
    cons: ["Weak against sharp attacks", "Complex combos", "High skill requirement"],
    mastery: [
      { level: 1, requirement: "Rubber Pistol" },
      { level: 100, requirement: "Rubber Rocket" },
      { level: 200, requirement: "Rubber Gatling" },
      { level: 300, requirement: "Rubber Storm" },
    ],
  },
  {
    id: "ghost",
    name: "Ghost",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/4e/GhostFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Ghost",
    rarity: "Rare",
    type: "Paramecia",
    value: 940000,
    awakened: false,
    robux: 9400,
    description: "The Ghost fruit allows the user to become incorporeal and phase through attacks.",
    abilities: ["Ghost Form", "Spectral Claws", "Phantom Strike"],
    pros: ["Phase through attacks", "Invisibility", "Good for escaping"],
    cons: ["Low damage", "Complex mechanics", "Hard to master"],
    mastery: [
      { level: 1, requirement: "Ghost Form" },
      { level: 100, requirement: "Spectral Claws" },
      { level: 200, requirement: "Phantom Strike" },
    ],
  },
  {
    id: "quake",
    name: "Quake",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/23/QuakeFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Quake",
    rarity: "Legendary",
    type: "Paramecia",
    value: 1000000,
    awakened: true,
    robux: 10000,
    description: "The Quake fruit allows the user to create powerful earthquakes.",
    abilities: ["Seaquake", "Spatial Shockwave", "Air Crusher", "World Destroyer"],
    pros: ["Extremely high damage", "Area attacks", "Great for grinding"],
    cons: ["Slow attacks", "High mastery requirements", "Expensive awakening"],
    mastery: [
      { level: 1, requirement: "Seaquake" },
      { level: 100, requirement: "Spatial Shockwave" },
      { level: 200, requirement: "Air Crusher" },
      { level: 300, requirement: "World Destroyer" },
    ],
  },
  {
    id: "love",
    name: "Love",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/5a/LoveFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Love",
    rarity: "Legendary",
    type: "Paramecia",
    value: 1300000,
    awakened: false,
    robux: 13000,
    description: "The Love fruit allows the user to create heart-shaped projectiles and charm enemies.",
    abilities: ["Heart Shot", "Charm", "Love Barrier"],
    pros: ["Unique charm effects", "Good damage", "Area control"],
    cons: ["Limited range", "Situational abilities", "No awakening yet"],
    mastery: [
      { level: 1, requirement: "Heart Shot" },
      { level: 100, requirement: "Charm" },
      { level: 200, requirement: "Love Barrier" },
    ],
  },
  {
    id: "creation",
    name: "Creation",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/4/45/CreationFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Creation",
    rarity: "Legendary",
    type: "Paramecia",
    value: 1400000,
    awakened: false,
    robux: 14000,
    description: "The Creation fruit allows the user to create and manipulate various objects.",
    abilities: ["Object Creation", "Material Manipulation", "Construct Barrier"],
    pros: ["Versatile abilities", "Creative combat", "Good utility"],
    cons: ["Complex mechanics", "High skill requirement", "No awakening yet"],
    mastery: [
      { level: 1, requirement: "Object Creation" },
      { level: 100, requirement: "Material Manipulation" },
      { level: 200, requirement: "Construct Barrier" },
    ],
  },
  {
    id: "spider",
    name: "Spider",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/82/SpiderFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Spider",
    rarity: "Legendary",
    type: "Zoan",
    value: 1600000,
    awakened: false,
    robux: 16000,
    description: "The Spider fruit is a Zoan that allows the user to transform into a spider.",
    abilities: ["Web Shot", "Spider Swarm", "Venom Bite"],
    pros: ["Web trapping", "Good mobility", "Poison effects"],
    cons: ["Complex mechanics", "Limited range", "No awakening yet"],
    mastery: [
      { level: 1, requirement: "Web Shot" },
      { level: 100, requirement: "Spider Swarm" },
      { level: 200, requirement: "Venom Bite" },
    ],
  },
  {
    id: "sound",
    name: "Sound",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/f4/SoundFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Sound",
    rarity: "Legendary",
    type: "Paramecia",
    value: 1700000,
    awakened: false,
    robux: 17000,
    description: "The Sound fruit allows the user to create and control sound waves.",
    abilities: ["Sound Wave", "Sonic Boom", "Echo Strike"],
    pros: ["Area damage", "Stun effects", "Good range"],
    cons: ["Complex timing", "No awakening yet", "High skill requirement"],
    mastery: [
      { level: 1, requirement: "Sound Wave" },
      { level: 100, requirement: "Sonic Boom" },
      { level: 200, requirement: "Echo Strike" },
    ],
  },
  {
    id: "portal",
    name: "Portal",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/e4/PortalFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Portal",
    rarity: "Legendary",
    type: "Paramecia",
    value: 1900000,
    awakened: false,
    robux: 19000,
    description: "The Portal fruit allows the user to create portals for teleportation.",
    abilities: ["Portal Creation", "Dimensional Rift", "Teleport"],
    pros: ["Excellent mobility", "Surprise attacks", "Escape utility"],
    cons: ["Low damage", "Complex mechanics", "No awakening yet"],
    mastery: [
      { level: 1, requirement: "Portal Creation" },
      { level: 100, requirement: "Dimensional Rift" },
      { level: 200, requirement: "Teleport" },
    ],
  },
  {
    id: "pain",
    name: "Pain",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/3/3f/PainFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Pain",
    rarity: "Legendary",
    type: "Paramecia",
    value: 2000000,
    awakened: false,
    robux: 20000,
    description: "The Pain fruit allows the user to manipulate and inflict pain.",
    abilities: ["Pain Infliction", "Agony Wave", "Suffering Strike"],
    pros: ["Damage over time", "Debuff effects", "Psychological warfare"],
    cons: ["Complex mechanics", "No awakening yet", "Situational"],
    mastery: [
      { level: 1, requirement: "Pain Infliction" },
      { level: 100, requirement: "Agony Wave" },
      { level: 200, requirement: "Suffering Strike" },
    ],
  },
  {
    id: "blizzard",
    name: "Blizzard",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/de/BlizzardFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Blizzard",
    rarity: "Legendary",
    type: "Logia",
    value: 2200000,
    awakened: false,
    robux: 22000,
    description: "The Blizzard fruit is a Logia that allows the user to create and control blizzards.",
    abilities: ["Blizzard Storm", "Ice Shards", "Frozen Domain"],
    pros: ["Logia immunity", "Area control", "Freeze effects"],
    cons: ["Slow attacks", "No awakening yet", "Weak against fire"],
    mastery: [
      { level: 1, requirement: "Blizzard Storm" },
      { level: 100, requirement: "Ice Shards" },
      { level: 200, requirement: "Frozen Domain" },
    ],
  },
  {
    id: "gravity",
    name: "Gravity",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/22/GravityFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Gravity",
    rarity: "Legendary",
    type: "Paramecia",
    value: 2500000,
    awakened: false,
    robux: 25000,
    description: "The Gravity fruit allows the user to manipulate gravitational forces.",
    abilities: ["Gravity Push", "Gravity Pull", "Meteor Rain"],
    pros: ["Area control", "High damage", "Unique mechanics"],
    cons: ["Complex controls", "No awakening yet", "High skill requirement"],
    mastery: [
      { level: 1, requirement: "Gravity Push" },
      { level: 100, requirement: "Gravity Pull" },
      { level: 200, requirement: "Meteor Rain" },
    ],
  },
  {
    id: "mammoth",
    name: "Mammoth",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/d3/MammothFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Mammoth",
    rarity: "Mythical",
    type: "Zoan",
    value: 2700000,
    awakened: false,
    robux: 27000,
    description: "The Mammoth fruit is a Mythical Zoan that transforms the user into a mammoth.",
    abilities: ["Mammoth Charge", "Tusk Strike", "Earthquake Stomp"],
    pros: ["Huge size", "High damage", "Knockback effects"],
    cons: ["Slow movement", "Big target", "No awakening yet"],
    mastery: [
      { level: 1, requirement: "Mammoth Charge" },
      { level: 100, requirement: "Tusk Strike" },
      { level: 200, requirement: "Earthquake Stomp" },
    ],
  },
  {
    id: "t-rex",
    name: "T-Rex",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/ea/T-RexFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/T-Rex",
    rarity: "Mythical",
    type: "Zoan",
    value: 2700000,
    awakened: false,
    robux: 27000,
    description: "The T-Rex fruit is a Mythical Zoan that transforms the user into a T-Rex.",
    abilities: ["Prehistoric Roar", "Bite Force", "Tail Whip"],
    pros: ["Extremely high damage", "Fear effects", "Massive size"],
    cons: ["Very slow", "Big target", "No awakening yet"],
    mastery: [
      { level: 1, requirement: "Prehistoric Roar" },
      { level: 100, requirement: "Bite Force" },
      { level: 200, requirement: "Tail Whip" },
    ],
  },
  {
    id: "gas",
    name: "Gas",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/7e/GasFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Gas",
    rarity: "Mythical",
    type: "Logia",
    value: 3100000,
    awakened: false,
    robux: 31000,
    description: "The Gas fruit is a Mythical Logia that allows the user to create and control toxic gas.",
    abilities: ["Gas Cloud", "Toxic Breath", "Poison Fog"],
    pros: ["Logia immunity", "Poison effects", "Area denial"],
    cons: ["No awakening yet", "Complex mechanics", "Environmental hazard"],
    mastery: [
      { level: 1, requirement: "Gas Cloud" },
      { level: 100, requirement: "Toxic Breath" },
      { level: 200, requirement: "Poison Fog" },
    ],
  },
  {
    id: "yeti",
    name: "Yeti",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/14/YetiFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Yeti",
    rarity: "Mythical",
    type: "Zoan",
    value: 2200000,
    awakened: false,
    robux: 22000,
    description: "The Yeti fruit is a Mythical Zoan that transforms the user into a yeti.",
    abilities: ["Ice Punch", "Blizzard Roar", "Frozen Slam"],
    pros: ["Ice effects", "High damage", "Freeze abilities"],
    cons: ["Slow movement", "No awakening yet", "Big target"],
    mastery: [
      { level: 1, requirement: "Ice Punch" },
      { level: 100, requirement: "Blizzard Roar" },
      { level: 200, requirement: "Frozen Slam" },
    ],
  },
  // Nouveaux fruits ajoutés avec les bonnes images
  {
    id: "kilo",
    name: "Kilo",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/6d/KiloFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Kilo",
    rarity: "Common",
    type: "Paramecia",
    value: 5000,
    awakened: false,
    robux: 50,
    description: "The Kilo fruit allows the user to change their weight from 1 to 10,000 kilograms.",
    abilities: ["Kilo Press", "Kilo Punch", "10,000 Kilo Press"],
    pros: ["Weight manipulation", "Good for early game", "Unique mechanics"],
    cons: ["Low damage", "Limited range", "Predictable attacks"],
    mastery: [
      { level: 1, requirement: "Kilo Press" },
      { level: 50, requirement: "Kilo Punch" },
      { level: 100, requirement: "10,000 Kilo Press" },
    ],
  },
  {
    id: "chop",
    name: "Chop",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/51/ChopFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Chop",
    rarity: "Common",
    type: "Paramecia",
    value: 30000,
    awakened: false,
    robux: 300,
    description: "The Chop fruit allows the user to be immune to swords and makes them immune to most sword attacks.",
    abilities: ["Chop", "Buzz Cut", "Helicopter Flight"],
    pros: ["Immunity to swords", "Good for early game", "Unique immunity"],
    cons: ["Weak against other attacks", "Low damage", "Limited use late game"],
    mastery: [
      { level: 1, requirement: "Chop" },
      { level: 50, requirement: "Buzz Cut" },
      { level: 100, requirement: "Helicopter Flight" },
    ],
  },
  {
    id: "falcon",
    name: "Falcon",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/7d/FalconFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Falcon",
    rarity: "Uncommon",
    type: "Zoan",
    value: 300000,
    awakened: false,
    robux: 3000,
    description: "The Falcon fruit is a Zoan that allows the user to transform into a falcon.",
    abilities: ["Falcon Flight", "Dive Bomb", "Wind Slash"],
    pros: ["Flight ability", "Good mobility", "Decent damage"],
    cons: ["Low health", "Weak against ranged attacks", "Limited ground combat"],
    mastery: [
      { level: 1, requirement: "Falcon Flight" },
      { level: 100, requirement: "Dive Bomb" },
      { level: 200, requirement: "Wind Slash" },
    ],
  },
  {
    id: "string",
    name: "String",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/a/ac/StringFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/String",
    rarity: "Rare",
    type: "Paramecia",
    value: 750000,
    awakened: true,
    robux: 7500,
    description: "The String fruit allows the user to create and control strings for various attacks.",
    abilities: ["String Path", "Heavenly Punishment", "God Thread", "Eternal White"],
    pros: ["Great mobility", "High damage", "Good for PvP", "Awakening available"],
    cons: ["Hard to master", "Complex combos", "High skill requirement"],
    mastery: [
      { level: 1, requirement: "String Path" },
      { level: 100, requirement: "Heavenly Punishment" },
      { level: 200, requirement: "God Thread" },
      { level: 300, requirement: "Eternal White" },
    ],
  },
  {
    id: "barrier",
    name: "Barrier",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/0/0a/BarrierFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Barrier",
    rarity: "Rare",
    type: "Paramecia",
    value: 800000,
    awakened: false,
    robux: 8000,
    description: "The Barrier fruit allows the user to create protective barriers and offensive barrier attacks.",
    abilities: ["Barrier Wall", "Barrier Prison", "Barrier Crash"],
    pros: ["Great defense", "Area control", "Good for support"],
    cons: ["Low damage", "Slow attacks", "Limited mobility"],
    mastery: [
      { level: 1, requirement: "Barrier Wall" },
      { level: 100, requirement: "Barrier Prison" },
      { level: 200, requirement: "Barrier Crash" },
    ],
  },
  {
    id: "revive",
    name: "Revive",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/d7/ReviveFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Revive",
    rarity: "Rare",
    type: "Paramecia",
    value: 550000,
    awakened: false,
    robux: 5500,
    description: "The Revive fruit allows the user to heal themselves and others, and has resurrection abilities.",
    abilities: ["Heal", "Resurrection", "Soul Steal"],
    pros: ["Healing abilities", "Support utility", "Unique mechanics"],
    cons: ["Low damage", "Support focused", "Limited solo potential"],
    mastery: [
      { level: 1, requirement: "Heal" },
      { level: 100, requirement: "Resurrection" },
      { level: 200, requirement: "Soul Steal" },
    ],
  },
  {
    id: "paw",
    name: "Paw",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/d/dc/PawFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Paw",
    rarity: "Legendary",
    type: "Paramecia",
    value: 2300000,
    awakened: false,
    robux: 23000,
    description: "The Paw fruit allows the user to repel anything with paw-shaped pads, including air and attacks.",
    abilities: ["Paw Barrage", "Heavy Paw", "Torture", "Self Repel"],
    pros: ["High damage", "Great mobility", "Knockback effects", "Unique mechanics"],
    cons: ["Hard to master", "Complex timing", "No awakening yet"],
    mastery: [
      { level: 1, requirement: "Paw Barrage" },
      { level: 100, requirement: "Heavy Paw" },
      { level: 200, requirement: "Torture" },
      { level: 300, requirement: "Self Repel" },
    ],
  },
  {
    id: "door",
    name: "Door",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/5b/DoorFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Door",
    rarity: "Rare",
    type: "Paramecia",
    value: 1000000,
    awakened: false,
    robux: 10000,
    description: "The Door fruit allows the user to create doors in the air for transportation and attacks.",
    abilities: ["Door", "Door Party", "Dimensional Door"],
    pros: ["Excellent mobility", "Surprise attacks", "Escape utility"],
    cons: ["Low damage", "Complex mechanics", "Situational"],
    mastery: [
      { level: 1, requirement: "Door" },
      { level: 100, requirement: "Door Party" },
      { level: 200, requirement: "Dimensional Door" },
    ],
  },
  {
    id: "human",
    name: "Human",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b4/HumanFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Human",
    rarity: "Common",
    type: "Zoan",
    value: 50000,
    awakened: false,
    robux: 500,
    description: "The Human fruit is a Zoan that enhances the user's physical capabilities and intelligence.",
    abilities: ["Enhanced Strength", "Enhanced Speed", "Enhanced Intelligence"],
    pros: ["Stat boosts", "No transformation needed", "Always active"],
    cons: ["No special attacks", "Boring abilities", "Low value"],
    mastery: [
      { level: 1, requirement: "Enhanced Strength" },
      { level: 50, requirement: "Enhanced Speed" },
      { level: 100, requirement: "Enhanced Intelligence" },
    ],
  },
  {
    id: "soul",
    name: "Soul",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b5/SoulFruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Soul",
    rarity: "Mythical",
    type: "Paramecia",
    value: 3300000,
    awakened: false,
    robux: 33000,
    description: "The Soul fruit is a Mythical Paramecia that allows the user to manipulate souls and life force.",
    abilities: ["Soul Extraction", "Life Drain", "Soul Prison", "Spectral Army"],
    pros: ["Life steal abilities", "Soul manipulation", "High damage", "Unique mechanics"],
    cons: ["Very expensive", "Complex mechanics", "No awakening yet"],
    mastery: [
      { level: 1, requirement: "Soul Extraction" },
      { level: 100, requirement: "Life Drain" },
      { level: 200, requirement: "Soul Prison" },
      { level: 300, requirement: "Spectral Army" },
    ],
  },
  {
    id: "dragon-classic",
    name: "Dragon (Classic)",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/76/Dragon_%28Classic%29Fruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Dragon_(Classic)",
    rarity: "Mythical",
    type: "Zoan",
    value: 3600000,
    awakened: false,
    robux: 36000,
    description: "The classic version of the Dragon fruit with traditional dragon abilities and appearance.",
    abilities: ["Dragon Breath", "Dragon Claw", "Dragon Flight", "Ancient Roar"],
    pros: ["Classic dragon powers", "Flight ability", "High damage", "Nostalgic design"],
    cons: ["Very expensive", "No awakening", "Slower than modern Dragon"],
    mastery: [
      { level: 1, requirement: "Dragon Breath" },
      { level: 100, requirement: "Dragon Claw" },
      { level: 200, requirement: "Dragon Flight" },
      { level: 300, requirement: "Ancient Roar" },
    ],
  },
  {
    id: "meme",
    name: "Meme",
    image: "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b9/Meme_Fruit.png/",
    wikiUrl: "https://blox-fruits.fandom.com/wiki/Meme",
    rarity: "Legendary",
    type: "Paramecia",
    value: 1500000,
    awakened: false,
    robux: 15000,
    description: "The Meme fruit is a joke fruit with unpredictable and humorous abilities that can surprise enemies.",
    abilities: ["Random Effect", "Meme Beam", "Chaos Mode", "Troll Attack"],
    pros: ["Unpredictable abilities", "Fun to use", "Surprise factor", "Unique mechanics"],
    cons: ["Inconsistent damage", "Hard to master", "Joke fruit", "No awakening"],
    mastery: [
      { level: 1, requirement: "Random Effect" },
      { level: 100, requirement: "Meme Beam" },
      { level: 200, requirement: "Chaos Mode" },
      { level: 300, requirement: "Troll Attack" },
    ],
  },
]

// Fonction améliorée pour fusionner les données
export const mergeFruitData = (staticFruit: Fruit, apiData?: ApiFruit): Fruit => {
  if (apiData && apiData.name.toLowerCase() === staticFruit.name.toLowerCase()) {
    return {
      ...staticFruit,
      value: apiData.value,
      rarity: apiData.rarity as any,
      type: apiData.type as any,
      awakened: apiData.awakened,
      // Nouvelles données de l'API
      stock: apiData.stock,
      demand: apiData.demand as any,
      trend: apiData.trend as any,
      lastSeen: apiData.lastSeen,
      spawnRate: apiData.spawnRate,
      popularTrades: apiData.popularTrades,
      priceHistory: apiData.priceHistory,
    }
  }
  return staticFruit
}

// Fonction pour obtenir la couleur de la tendance
export const getTrendColor = (trend?: string) => {
  switch (trend?.toLowerCase()) {
    case "rising":
      return "text-green-400"
    case "falling":
      return "text-red-400"
    default:
      return "text-gray-400"
  }
}

// Fonction pour obtenir la couleur de la demande
export const getDemandColor = (demand?: string) => {
  switch (demand?.toLowerCase()) {
    case "very high":
      return "text-red-500"
    case "high":
      return "text-orange-500"
    case "medium":
      return "text-yellow-500"
    case "low":
      return "text-green-500"
    default:
      return "text-gray-500"
  }
}

export const getRarityColor = (rarity: string) => {
  switch (rarity.toLowerCase()) {
    case "mythical":
      return "from-purple-500 to-pink-500"
    case "legendary":
      return "from-orange-500 to-red-500"
    case "rare":
      return "from-blue-500 to-cyan-500"
    case "uncommon":
      return "from-green-500 to-emerald-500"
    default:
      return "from-gray-500 to-slate-500"
  }
}

export const getTypeColor = (type: string) => {
  switch (type.toLowerCase()) {
    case "logia":
      return "bg-blue-500"
    case "paramecia":
      return "bg-green-500"
    case "zoan":
      return "bg-orange-500"
    default:
      return "bg-gray-500"
  }
}
