"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { fruitsData, getRarityColor } from "@/lib/fruits-data"

interface TierListProps {
  dict: any
  lang: string
}

export function TierList({ dict }: TierListProps) {
  const tiers = {
    S: fruitsData.filter((f) => ["Leopard", "Dragon", "Spirit", "Kitsune"].includes(f.name)),
    A: fruitsData.filter((f) => ["Venom", "Control", "Shadow", "Dough"].includes(f.name)),
    B: fruitsData.filter((f) => ["Rumble", "Phoenix", "Buddha", "Magma"].includes(f.name)),
    C: fruitsData.filter((f) => ["Light", "Dark", "Sand", "Ice", "Flame"].includes(f.name)),
    D: fruitsData.filter(
      (f) =>
        ![
          "<PERSON><PERSON>",
          "<PERSON>",
          "<PERSON>",
          "Kitsun<PERSON>",
          "<PERSON><PERSON><PERSON>",
          "<PERSON>",
          "<PERSON>",
          "<PERSON><PERSON>",
          "<PERSON>umble",
          "<PERSON>",
          "Buddha",
          "Magma",
          "<PERSON>",
          "<PERSON>",
          "Sand",
          "Ice",
          "Flame",
        ].includes(f.name),
    ),
  }

  const tierColors = {
    S: "from-red-500 to-pink-500",
    A: "from-orange-500 to-yellow-500",
    B: "from-green-500 to-emerald-500",
    C: "from-blue-500 to-cyan-500",
    D: "from-gray-500 to-slate-500",
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-5xl font-bold text-white mb-4">Blox Fruits Tier List</h1>
        <p className="text-xl text-gray-400">
          Ranking based on PvP performance, grinding efficiency, and overall utility
        </p>
      </div>

      <div className="space-y-8">
        {Object.entries(tiers).map(([tier, fruits]) => (
          <Card key={tier} className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-4">
                <div
                  className={`w-16 h-16 rounded-lg bg-gradient-to-r ${tierColors[tier as keyof typeof tierColors]} flex items-center justify-center`}
                >
                  <span className="text-2xl font-bold text-white">{tier}</span>
                </div>
                <span className="text-white text-2xl">Tier {tier}</span>
                <Badge variant="outline" className="border-white/20 text-white">
                  {fruits.length} fruits
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {fruits.map((fruit) => (
                  <div
                    key={fruit.id}
                    className="bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-all duration-300 text-center"
                  >
                    <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-white/10 flex items-center justify-center overflow-hidden">
                      <img
                        src={fruit.image || "/placeholder.svg"}
                        alt={fruit.name}
                        className="w-12 h-12 object-contain"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement
                          target.src = "/placeholder.svg?height=48&width=48"
                        }}
                      />
                    </div>
                    <h3 className="text-white font-semibold text-sm mb-2">{fruit.name}</h3>
                    <Badge className={`${getRarityColor(fruit.rarity)} bg-gradient-to-r text-white border-0 text-xs`}>
                      {fruit.rarity}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-12 text-center">
        <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
          <CardContent className="p-8">
            <h3 className="text-2xl font-bold text-white mb-4">Tier Explanation</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4 text-left">
              <div>
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded bg-gradient-to-r from-red-500 to-pink-500 flex items-center justify-center mr-2">
                    <span className="text-white font-bold text-sm">S</span>
                  </div>
                  <span className="text-white font-semibold">S Tier</span>
                </div>
                <p className="text-gray-400 text-sm">Meta fruits with exceptional performance in all areas</p>
              </div>
              <div>
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded bg-gradient-to-r from-orange-500 to-yellow-500 flex items-center justify-center mr-2">
                    <span className="text-white font-bold text-sm">A</span>
                  </div>
                  <span className="text-white font-semibold">A Tier</span>
                </div>
                <p className="text-gray-400 text-sm">Excellent fruits with high damage and utility</p>
              </div>
              <div>
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mr-2">
                    <span className="text-white font-bold text-sm">B</span>
                  </div>
                  <span className="text-white font-semibold">B Tier</span>
                </div>
                <p className="text-gray-400 text-sm">Good fruits suitable for most content</p>
              </div>
              <div>
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center mr-2">
                    <span className="text-white font-bold text-sm">C</span>
                  </div>
                  <span className="text-white font-semibold">C Tier</span>
                </div>
                <p className="text-gray-400 text-sm">Average fruits with specific use cases</p>
              </div>
              <div>
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded bg-gradient-to-r from-gray-500 to-slate-500 flex items-center justify-center mr-2">
                    <span className="text-white font-bold text-sm">D</span>
                  </div>
                  <span className="text-white font-semibold">D Tier</span>
                </div>
                <p className="text-gray-400 text-sm">Below average fruits, mainly for early game</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
