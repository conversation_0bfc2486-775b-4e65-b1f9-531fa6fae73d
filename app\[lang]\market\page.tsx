import { getDictionary } from "../dictionaries"
import { MarketDashboard } from "@/components/market-dashboard"

export default async function MarketPage({
  params,
}: {
  params: Promise<{ lang: string }>
}) {
  const { lang } = await params
  const dict = await getDictionary(lang as any)

  return (
    <div className="container mx-auto px-4 py-20">
      <div className="text-center mb-12">
        <h1 className="text-5xl font-bold text-white mb-4">Market Dashboard</h1>
        <p className="text-xl text-gray-400">Real-time market data and trading insights</p>
      </div>
      <MarketDashboard dict={dict} />
    </div>
  )
}
