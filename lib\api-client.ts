// Singleton pour partager les données entre composants
class FruitsApiClient {
  private static instance: FruitsApiClient
  private data: any = null
  private lastFetch = 0
  private subscribers: ((data: any) => void)[] = []
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  static getInstance(): FruitsApiClient {
    if (!FruitsApiClient.instance) {
      FruitsApiClient.instance = new FruitsApiClient()
    }
    return FruitsApiClient.instance
  }

  async getFruits(): Promise<any> {
    const now = Date.now()

    // Return cached data if still fresh
    if (this.data && now - this.lastFetch < this.CACHE_DURATION) {
      return this.data
    }

    try {
      const response = await fetch("/api/fruits")
      const newData = await response.json()

      this.data = newData
      this.lastFetch = now

      // Notify all subscribers
      this.subscribers.forEach((callback) => callback(newData))

      return newData
    } catch (error) {
      console.error("Error fetching fruits:", error)
      return this.data || { fruits: [], error: "Failed to fetch" }
    }
  }

  subscribe(callback: (data: any) => void): () => void {
    this.subscribers.push(callback)

    // Return unsubscribe function
    return () => {
      this.subscribers = this.subscribers.filter((cb) => cb !== callback)
    }
  }

  // Start auto-refresh
  startAutoRefresh(): void {
    setInterval(() => {
      this.getFruits()
    }, this.CACHE_DURATION)
  }
}

export const fruitsApiClient = FruitsApiClient.getInstance()
