import { notFound } from "next/navigation"
import { getDictionary } from "../../dictionaries"
import { fruitsData } from "@/lib/fruits-data"
import { FruitDetail } from "@/components/fruit-detail"

export async function generateStaticParams() {
  const fruits = fruitsData.map((fruit) => ({
    id: fruit.id,
  }))

  // Generate for all languages
  const languages = ["en", "fr", "es", "ar"]
  const params = []

  for (const lang of languages) {
    for (const fruit of fruits) {
      params.push({
        lang,
        id: fruit.id,
      })
    }
  }

  return params
}

export default async function FruitPage({
  params,
}: {
  params: Promise<{ lang: string; id: string }>
}) {
  const { lang, id } = await params
  const dict = await getDictionary(lang as any)

  const fruit = fruitsData.find((f) => f.id === id)

  if (!fruit) {
    notFound()
  }

  return <FruitDetail fruit={fruit} dict={dict} lang={lang} />
}
