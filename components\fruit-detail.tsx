import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  ExternalLink,
  Star,
  Zap,
  Shield,
  Sword,
  Package,
  Users,
  TrendingUp,
  TrendingDown,
  Minus,
} from "lucide-react"
import { type Fruit, getRarityColor, getTypeColor, getDemandColor, getTrendColor } from "@/lib/fruits-data"
import { Progress } from "@/components/ui/progress"

interface FruitDetailProps {
  fruit: Fruit
  dict: any
  lang: string
}

export function FruitDetail({ fruit, dict, lang }: FruitDetailProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Back Button */}
        <div className="mb-8">
          <Button asChild variant="outline" className="border-white/20 text-white hover:bg-white/10">
            <Link href={`/${lang}`}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              {dict.fruit.backToFruits}
            </Link>
          </Button>
        </div>

        {/* Hero Section */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          <div className="text-center lg:text-left">
            <div className="flex justify-center lg:justify-start mb-6">
              <div className="relative">
                <div className="w-48 h-48 rounded-full bg-white/10 flex items-center justify-center overflow-hidden">
                  <img
                    src={fruit.image || "/placeholder.svg"}
                    alt={fruit.name}
                    className="w-40 h-40 object-contain"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src = "/placeholder.svg?height=160&width=160"
                    }}
                  />
                </div>
                {fruit.awakened && (
                  <div className="absolute -top-4 -right-4">
                    <div className="bg-yellow-400 rounded-full p-2">
                      <Star className="w-8 h-8 text-yellow-900 fill-current" />
                    </div>
                  </div>
                )}
              </div>
            </div>

            <h1 className="text-5xl font-bold text-white mb-4">{fruit.name}</h1>
            <p className="text-xl text-gray-300 mb-6">{fruit.description}</p>

            <div className="flex flex-wrap justify-center lg:justify-start gap-3 mb-6">
              <Badge
                className={`${getRarityColor(fruit.rarity)} bg-gradient-to-r text-white border-0 text-lg px-4 py-2`}
              >
                {fruit.rarity}
              </Badge>
              <Badge className={`${getTypeColor(fruit.type)} text-white text-lg px-4 py-2`}>{fruit.type}</Badge>
              {fruit.awakened && (
                <Badge className="bg-yellow-500 text-yellow-900 text-lg px-4 py-2">{dict.fruit.awakened}</Badge>
              )}
            </div>

            <div className="flex gap-4 justify-center lg:justify-start">
              <Button
                asChild
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
              >
                <a href={fruit.wikiUrl} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  {dict.fruit.viewWiki}
                </a>
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 gap-4">
            <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-green-400 mb-2">${fruit.value.toLocaleString()}</div>
                <div className="text-gray-400">{dict.fruit.bellyValue}</div>
              </CardContent>
            </Card>

            <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">{fruit.robux.toLocaleString()}</div>
                <div className="text-gray-400">{dict.fruit.robuxValue}</div>
              </CardContent>
            </Card>

            <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-purple-400 mb-2">{fruit.type}</div>
                <div className="text-gray-400">{dict.fruit.fruitType}</div>
              </CardContent>
            </Card>

            <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-orange-400 mb-2">{fruit.rarity}</div>
                <div className="text-gray-400">{dict.fruit.rarity}</div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Enhanced Market Information */}
        {(fruit.stock !== undefined || fruit.demand || fruit.trend) && (
          <div className="grid lg:grid-cols-3 gap-4 mb-8">
            {fruit.stock !== undefined && (
              <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                <CardContent className="p-6 text-center">
                  <Package className="w-8 h-8 mx-auto mb-2 text-blue-400" />
                  <div className="text-2xl font-bold text-white mb-2">{fruit.stock}/100</div>
                  <div className="text-gray-400 mb-2">Stock Level</div>
                  <Progress value={fruit.stock} className="h-2" />
                </CardContent>
              </Card>
            )}

            {fruit.demand && (
              <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                <CardContent className="p-6 text-center">
                  <Users className="w-8 h-8 mx-auto mb-2 text-orange-400" />
                  <div className={`text-2xl font-bold mb-2 ${getDemandColor(fruit.demand)}`}>{fruit.demand}</div>
                  <div className="text-gray-400">Market Demand</div>
                </CardContent>
              </Card>
            )}

            {fruit.trend && (
              <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                <CardContent className="p-6 text-center">
                  {fruit.trend === "Rising" ? (
                    <TrendingUp className="w-8 h-8 mx-auto mb-2 text-green-400" />
                  ) : fruit.trend === "Falling" ? (
                    <TrendingDown className="w-8 h-8 mx-auto mb-2 text-red-400" />
                  ) : (
                    <Minus className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  )}
                  <div className={`text-2xl font-bold mb-2 ${getTrendColor(fruit.trend)}`}>{fruit.trend}</div>
                  <div className="text-gray-400">Price Trend</div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Detailed Information */}
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Abilities */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Zap className="w-6 h-6 mr-2 text-yellow-400" />
                {dict.fruit.abilities}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {fruit.abilities.map((ability, index) => (
                  <div key={index} className="flex items-center p-3 bg-white/5 rounded-lg">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white font-bold text-sm">{index + 1}</span>
                    </div>
                    <span className="text-white">{ability}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Mastery Requirements */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Star className="w-6 h-6 mr-2 text-yellow-400" />
                {dict.fruit.masteryRequirements}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {fruit.mastery.map((req, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <span className="text-white">{req.requirement}</span>
                    <Badge variant="outline" className="border-white/20 text-white">
                      Level {req.level}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Pros */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Shield className="w-6 h-6 mr-2 text-green-400" />
                {dict.fruit.pros}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {fruit.pros.map((pro, index) => (
                  <div key={index} className="flex items-center text-green-400">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                    {pro}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Cons */}
          <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Sword className="w-6 h-6 mr-2 text-red-400" />
                {dict.fruit.cons}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {fruit.cons.map((con, index) => (
                  <div key={index} className="flex items-center text-red-400">
                    <div className="w-2 h-2 bg-red-400 rounded-full mr-3"></div>
                    {con}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
