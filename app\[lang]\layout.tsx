import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "../globals.css"
import { getDictionary } from "./dictionaries"
import { ThemeProvider } from "@/components/theme-provider"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"

const inter = Inter({ subsets: ["latin"] })

export async function generateStaticParams() {
  return [{ lang: "en" }, { lang: "fr" }, { lang: "es" }, { lang: "ar" }]
}

export const metadata: Metadata = {
  title: "Blox Fruits Values - Trading Calculator & Price Guide",
  description:
    "Complete Blox Fruits trading values, calculator, and price guide. Updated every 5 minutes with accurate fruit values.",
}

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode
  params: Promise<{ lang: string }>
}) {
  const { lang } = await params
  const dict = await getDictionary(lang as any)

  return (
    <html lang={lang} dir={lang === "ar" ? "rtl" : "ltr"}>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem disableTransitionOnChange>
          <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
            <Header lang={lang} dict={dict} />
            <main>{children}</main>
            <Footer lang={lang} dict={dict} />
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
