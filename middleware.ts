import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

const locales = ["en", "fr", "es", "ar"]
const defaultLocale = "en"

function getLocale(request: NextRequest) {
  // Check for bot user agents and redirect to English
  const userAgent = request.headers.get("user-agent") || ""
  const isBot = /bot|crawler|spider|crawling/i.test(userAgent)

  if (isBot) {
    return defaultLocale
  }

  const acceptLanguage = request.headers.get("accept-language")
  if (!acceptLanguage) return defaultLocale

  const languages = acceptLanguage.split(",").map((lang) => lang.split(";")[0].trim())

  for (const lang of languages) {
    if (locales.includes(lang)) return lang
    const shortLang = lang.split("-")[0]
    if (locales.includes(shortLang)) return shortLang
  }

  return defaultLocale
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip internal paths and API routes
  if (pathname.startsWith("/_next") || pathname.startsWith("/api") || pathname.includes(".")) {
    return
  }

  // Check if pathname already has a locale
  const pathnameHasLocale = locales.some((locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`)

  // If pathname already has a locale, continue
  if (pathnameHasLocale) return

  // Handle root path - redirect to detected language
  if (pathname === "/") {
    const locale = getLocale(request)
    request.nextUrl.pathname = `/${locale}`
    return NextResponse.redirect(request.nextUrl)
  }

  // For any other path without locale, redirect to detected language + path
  const locale = getLocale(request)
  request.nextUrl.pathname = `/${locale}${pathname}`
  return NextResponse.redirect(request.nextUrl)
}

export const config = {
  matcher: ["/((?!_next|api|favicon.ico|.*\\.).*)"],
}
