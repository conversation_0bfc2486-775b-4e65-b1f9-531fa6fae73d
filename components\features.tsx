import { Clock, Calculator, Database } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

interface FeaturesProps {
  dict: any
}

export function Features({ dict }: FeaturesProps) {
  const features = [
    {
      icon: Clock,
      title: dict.features.realtime.title,
      description: dict.features.realtime.description,
    },
    {
      icon: Calculator,
      title: dict.features.calculator.title,
      description: dict.features.calculator.description,
    },
    {
      icon: Database,
      title: dict.features.comprehensive.title,
      description: dict.features.comprehensive.description,
    },
  ]

  return (
    <section className="py-20 px-4">
      <div className="container mx-auto">
        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="bg-white/5 border-white/10 backdrop-blur-sm hover:bg-white/10 transition-all duration-300"
            >
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
                <p className="text-gray-400">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
