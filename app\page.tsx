import { redirect } from "next/navigation"
import { headers } from "next/headers"

export default async function RootPage() {
  const headersList = await headers()
  const acceptLanguage = headersList.get("accept-language") || ""
  const userAgent = headersList.get("user-agent") || ""

  // Check for bot user agents
  const isBot = /bot|crawler|spider|crawling/i.test(userAgent)

  if (isBot) {
    redirect("/en")
  }

  // Detect language from accept-language header
  const locales = ["en", "fr", "es", "ar"]
  const languages = acceptLanguage.split(",").map((lang) => lang.split(";")[0].trim())

  for (const lang of languages) {
    if (locales.includes(lang)) {
      redirect(`/${lang}`)
    }
    const shortLang = lang.split("-")[0]
    if (locales.includes(shortLang)) {
      redirect(`/${shortLang}`)
    }
  }

  // Default to English
  redirect("/en")
}
