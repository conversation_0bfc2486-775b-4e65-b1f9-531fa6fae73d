"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, Star } from "lucide-react"

interface Fruit {
  name: string
  rarity: string
  type: string
  value: number
  awakened: boolean
  image: string
}

interface FruitsListProps {
  dict: any
  lang: string
}

export function FruitsList({ dict }: FruitsListProps) {
  const [fruits, setFruits] = useState<Fruit[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchFruits = async () => {
      try {
        const response = await fetch("/api/fruits")

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        console.log("Fetched data:", data) // Debug log

        // Handle different data structures
        if (data.fruits && Array.isArray(data.fruits)) {
          setFruits(data.fruits)
        } else if (Array.isArray(data)) {
          setFruits(data)
        } else {
          console.warn("Unexpected data structure:", data)
          setFruits([])
        }
      } catch (error) {
        console.error("Error fetching fruits:", error)
        // Set empty array on error
        setFruits([])
      } finally {
        setLoading(false)
      }
    }

    fetchFruits()

    // Set up interval to refetch every 1 minute - Updated from 5 minutes
    const interval = setInterval(fetchFruits, 1 * 60 * 1000)

    return () => clearInterval(interval)
  }, [])

  const filteredFruits = fruits.filter((fruit) => fruit.name.toLowerCase().includes(searchTerm.toLowerCase()))

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case "mythical":
        return "bg-purple-500"
      case "legendary":
        return "bg-orange-500"
      case "rare":
        return "bg-blue-500"
      case "uncommon":
        return "bg-green-500"
      default:
        return "bg-gray-500"
    }
  }

  if (loading) {
    return (
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center text-white">Loading fruits...</div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-white mb-4">{dict.fruits.title}</h2>
          <div className="max-w-md mx-auto relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder={dict.fruits.search}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredFruits.map((fruit, index) => (
            <Card
              key={index}
              className="bg-white/5 border-white/10 backdrop-blur-sm hover:bg-white/10 transition-all duration-300 group"
            >
              <CardContent className="p-6">
                <div className="relative mb-4">
                  <img
                    src={fruit.image || "/placeholder.svg"}
                    alt={fruit.name}
                    className="w-full h-32 object-cover rounded-lg"
                  />
                  {fruit.awakened && (
                    <div className="absolute top-2 right-2">
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                    </div>
                  )}
                </div>

                <h3 className="text-lg font-semibold text-white mb-2">{fruit.name}</h3>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">{dict.fruits.rarity}:</span>
                    <Badge className={`${getRarityColor(fruit.rarity)} text-white`}>{fruit.rarity}</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">{dict.fruits.type}:</span>
                    <span className="text-white text-sm">{fruit.type}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">{dict.fruits.value}:</span>
                    <span className="text-green-400 font-semibold">{fruit.value.toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
