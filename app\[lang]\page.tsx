import { getDictionary } from "./dictionaries"
import { <PERSON> } from "@/components/hero"
import { FruitsGrid } from "@/components/fruits-grid"
import { StatsSection } from "@/components/stats-section"

export default async function Home({
  params,
}: {
  params: Promise<{ lang: string }>
}) {
  const { lang } = await params
  const dict = await getDictionary(lang as any)

  return (
    <div className="min-h-screen">
      <Hero dict={dict} lang={lang} />
      <StatsSection dict={dict} />
      <FruitsGrid dict={dict} lang={lang} />
    </div>
  )
}
