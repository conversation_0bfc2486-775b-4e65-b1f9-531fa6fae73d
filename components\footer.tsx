import Link from "next/link"
import { Apple, Github, Twitter, DiscIcon as Discord } from "lucide-react"

interface FooterProps {
  lang: string
  dict: any
}

export function Footer({ lang, dict }: FooterProps) {
  const links = [
    { name: dict.nav.home, href: `/${lang}` },
    { name: dict.nav.calculator, href: `/${lang}/calculator` },
    { name: dict.nav.fruits, href: `/${lang}/fruits` },
  ]

  return (
    <footer className="bg-black/40 backdrop-blur-md border-t border-white/10 mt-20">
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          <div className="col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Apple className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-white">Blox Fruits Values</span>
            </div>
            <p className="text-gray-400 mb-6 max-w-md">{dict.footer.description}</p>
          </div>

          <div>
            <h3 className="text-white font-semibold mb-4">{dict.footer.links}</h3>
            <ul className="space-y-2">
              {links.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="text-gray-400 hover:text-white transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-white font-semibold mb-4">{dict.footer.social}</h3>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Github className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Discord className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-white/10 mt-8 pt-8 text-center">
          <p className="text-gray-400">{dict.footer.copyright}</p>
        </div>
      </div>
    </footer>
  )
}
