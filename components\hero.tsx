import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Search, Calculator, TrendingUp, BarChart3 } from "lucide-react"

interface HeroProps {
  dict: any
  lang: string
}

export function Hero({ dict, lang }: HeroProps) {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-20"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 text-center px-4 max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-6xl md:text-8xl font-bold mb-6 bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent">
            Blox Fruits
          </h1>
          <h2 className="text-3xl md:text-5xl font-bold text-white mb-4">{dict.hero.title}</h2>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">{dict.hero.description}</p>
        </div>

        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
          <Button
            size="lg"
            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 text-lg"
            asChild
          >
            <Link href={`/${lang}#fruits`}>
              <Search className="w-6 h-6 mr-2" />
              {dict.hero.exploreFruits}
            </Link>
          </Button>

          <Button
            size="lg"
            variant="outline"
            className="border-white/30 text-white hover:bg-white/10 px-8 py-4 text-lg backdrop-blur-sm"
            asChild
          >
            <Link href={`/${lang}/calculator`}>
              <Calculator className="w-6 h-6 mr-2" />
              {dict.hero.calculator}
            </Link>
          </Button>

          <Button
            size="lg"
            variant="outline"
            className="border-white/30 text-white hover:bg-white/10 px-8 py-4 text-lg backdrop-blur-sm"
            asChild
          >
            <Link href={`/${lang}/tier-list`}>
              <TrendingUp className="w-6 h-6 mr-2" />
              {dict.hero.tierList}
            </Link>
          </Button>

          <Button
            size="lg"
            variant="outline"
            className="border-white/30 text-white hover:bg-white/10 px-8 py-4 text-lg backdrop-blur-sm"
            asChild
          >
            <Link href={`/${lang}/market`}>
              <BarChart3 className="w-6 h-6 mr-2" />
              Market Dashboard
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
